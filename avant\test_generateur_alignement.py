#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test de l'alignement main_number = index dans le générateur
Vérifie que la modification du générateur fonctionne correctement
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'sauvegarde'))

from sauvegarde.generateur_parties_baccarat import GenerateurPartiesBaccarat

def test_alignement_generateur():
    """Test de l'alignement main_number = index dans le générateur"""
    
    print("🧪 TEST DE L'ALIGNEMENT GÉNÉRATEUR main_number = index")
    print("=" * 60)
    
    # Créer le générateur avec seed pour reproductibilité
    generateur = GenerateurPartiesBaccarat(seed=42)
    
    # Générer une partie de test (5 manches seulement)
    print("Génération d'une partie de test (5 manches P/B)...")
    partie = generateur.generer_partie(1, max_manches=5)
    
    print(f"\n📊 RÉSULTATS DE LA PARTIE :")
    print(f"Total mains générées : {len(partie.mains)}")
    print(f"Manches P/B : {partie.total_manches_pb}")
    print(f"TIE : {partie.total_ties}")
    
    print(f"\n🎯 VÉRIFICATION DE L'ALIGNEMENT :")
    print("Index | main_number | INDEX5 | INDEX3 | Status")
    print("-" * 55)
    
    alignement_correct = True
    
    for i, main in enumerate(partie.mains):
        status = ""
        if i == 0:
            # Vérifier que la main à l'index 0 est la main dummy
            if main.main_number == 0 and main.index5_combined == 'IGNORE_MAIN_0':
                status = "✅ DUMMY (IGNORÉ)"
            else:
                status = "❌ ERREUR DUMMY"
                alignement_correct = False
        else:
            # Vérifier que main_number = index
            if main.main_number == i:
                status = "✅ ALIGNÉ"
            else:
                status = f"❌ DÉCALÉ (attendu {i})"
                alignement_correct = False
        
        print(f"{i:5d} | {main.main_number:11d} | {main.index5_combined:6s} | {main.index3_result:6s} | {status}")
    
    print(f"\n🎯 RÉSULTAT DE L'ALIGNEMENT :")
    if alignement_correct:
        print("✅ ALIGNEMENT PARFAIT : main_number = index")
        print("✅ Main 0 dummy correctement placée à l'index 0")
        print("✅ Toutes les vraies mains sont alignées")
    else:
        print("❌ PROBLÈME D'ALIGNEMENT DÉTECTÉ")
        return False
    
    return True

def test_sequences_avec_generateur():
    """Test des séquences L4 et L5 avec les données du générateur"""
    
    print("\n🎯 TEST DES SÉQUENCES L4/L5 AVEC GÉNÉRATEUR")
    print("=" * 60)
    
    # Créer le générateur
    generateur = GenerateurPartiesBaccarat(seed=123)
    
    # Générer une partie avec plus de mains
    partie = generateur.generer_partie(1, max_manches=10)
    
    # Extraire la séquence complète (comme dans l'analyseur)
    sequence_complete = [main.index5_combined for main in partie.mains]
    
    print(f"Séquence complète générée ({len(sequence_complete)} éléments) :")
    for i, index5 in enumerate(sequence_complete[:8]):  # Afficher les 8 premiers
        status = "(IGNORÉ)" if i == 0 else ""
        print(f"  Index {i} = main {i} : {index5} {status}")
    
    # Test des séquences pour la main 5
    if len(sequence_complete) > 5:
        position_main = 5
        
        print(f"\nAnalyse de la main {position_main} :")
        
        # L4 : [main 2,3,4,5]
        seq_l4 = sequence_complete[position_main-3:position_main+1]
        print(f"  L4 (mains 2,3,4,5) : {seq_l4}")
        print(f"      Indices utilisés : [{position_main-3}:{position_main+1}] = [2:6]")
        
        # L5 : [main 1,2,3,4,5]
        seq_l5 = sequence_complete[position_main-4:position_main+1]
        print(f"  L5 (mains 1,2,3,4,5) : {seq_l5}")
        print(f"      Indices utilisés : [{position_main-4}:{position_main+1}] = [1:6]")
        
        # Vérification que main 0 n'est jamais incluse
        main_0_incluse = sequence_complete[0] in seq_l4 or sequence_complete[0] in seq_l5
        
        if not main_0_incluse:
            print(f"\n✅ Vérification : main 0 n'est jamais incluse dans les séquences")
            print(f"   L4 commence à l'index 2 (main 2)")
            print(f"   L5 commence à l'index 1 (main 1)")
            print(f"   Index 0 (main 0) est toujours ignoré")
            return True
        else:
            print(f"\n❌ ERREUR : main 0 est incluse dans les séquences !")
            return False
    else:
        print("⚠️ Pas assez de mains pour tester les séquences L4/L5")
        return False

def test_export_json_alignement():
    """Test de l'export JSON avec alignement"""
    
    print("\n📋 TEST DE L'EXPORT JSON AVEC ALIGNEMENT")
    print("=" * 60)
    
    # Créer le générateur
    generateur = GenerateurPartiesBaccarat(seed=456)
    
    # Générer une petite partie
    partie = generateur.generer_partie(1, max_manches=3)
    
    # Exporter en JSON
    filename_json = "test_alignement_generateur.json"
    generateur.exporter_json([partie], filename_json)
    
    # Lire le JSON généré pour vérifier
    import json
    with open(filename_json, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    mains = data['parties'][0]['mains']
    
    print(f"JSON généré avec {len(mains)} mains :")
    for i, main in enumerate(mains[:6]):  # Afficher les 6 premières
        main_number = main['main_number']
        index5 = main['index5_combined']
        index3 = main['index3_result']
        
        status = ""
        if i == 0:
            if main_number == 0 and index5 == 'IGNORE_MAIN_0':
                status = "✅ DUMMY"
            else:
                status = "❌ ERREUR"
        else:
            if main_number == i:
                status = "✅ ALIGNÉ"
            else:
                status = "❌ DÉCALÉ"
        
        print(f"  Index {i}: main_number={main_number}, index5={index5}, index3={index3} {status}")
    
    # Nettoyer le fichier de test
    os.remove(filename_json)
    
    print(f"\n✅ Export JSON testé avec succès")
    return True

if __name__ == "__main__":
    print("🚀 TESTS DE L'ALIGNEMENT GÉNÉRATEUR main_number = index")
    print("=" * 70)
    
    # Exécuter tous les tests
    tests = [
        ("Test alignement générateur", test_alignement_generateur),
        ("Test séquences L4/L5", test_sequences_avec_generateur),
        ("Test export JSON", test_export_json_alignement),
    ]
    
    resultats = []
    for nom_test, fonction_test in tests:
        try:
            resultat = fonction_test()
            resultats.append((nom_test, resultat))
            print(f"\n{'✅' if resultat else '❌'} {nom_test} : {'RÉUSSI' if resultat else 'ÉCHOUÉ'}")
        except Exception as e:
            resultats.append((nom_test, False))
            print(f"\n❌ {nom_test} : ERREUR - {e}")
    
    # Résumé final
    print("\n" + "=" * 70)
    print("📊 RÉSUMÉ DES TESTS GÉNÉRATEUR")
    reussites = sum(1 for _, resultat in resultats if resultat)
    total = len(resultats)
    
    for nom_test, resultat in resultats:
        print(f"  {'✅' if resultat else '❌'} {nom_test}")
    
    print(f"\n🎯 RÉSULTAT GLOBAL : {reussites}/{total} tests réussis")
    
    if reussites == total:
        print("🎉 TOUS LES TESTS GÉNÉRATEUR SONT RÉUSSIS !")
        print("✅ L'alignement main_number = index fonctionne dans le générateur")
        print("✅ Compatibilité parfaite avec l'analyseur modifié")
    else:
        print("⚠️ Certains tests ont échoué - vérification nécessaire")
