#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ANALYSEUR SIGNIFICANCE METRIQUES
===============================

Classe spécialisée pour identifier les métriques significatives pour les patterns S/O.
Remplace l'analyse de corrélations par des tests statistiques plus appropriés.

Auteur: Expert Statisticien
Date: 2025-06-26
Version: 1.0

Approche:
- Tests statistiques pour identifier les métriques discriminantes
- Analyse de l'impact prédictif sans seuils arbitraires
- Focus sur les patterns S/O uniquement
- Méthodes non-paramétriques robustes
"""

import numpy as np
import math
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from collections import defaultdict
import scipy.stats as stats


@dataclass
class ResultatSignificance:
    """Résultat d'analyse de significance pour une métrique."""
    nom_metrique: str
    test_statistique: str
    valeur_test: float
    p_value: float
    significatif: bool
    effet_size: float
    interpretation: str
    moyennes_par_pattern: Dict[str, float]
    ecarts_types_par_pattern: Dict[str, float]


@dataclass
class AnalyseDistribution:
    """Analyse de distribution d'une métrique par pattern."""
    pattern: str
    moyenne: float
    mediane: float
    ecart_type: float
    quartile_25: float
    quartile_75: float
    min_val: float
    max_val: float
    nb_observations: int


class TestsStatistiques:
    """
    Classe contenant les tests statistiques pour l'analyse de significance.
    """
    
    @staticmethod
    def test_mann_whitney(groupe_s: List[float], groupe_o: List[float]) -> Tuple[float, float]:
        """
        Test de Mann-Whitney U pour comparer deux groupes indépendants.
        Non-paramétrique, robuste aux outliers.
        
        Returns:
            Tuple[float, float]: (statistique U, p-value)
        """
        if len(groupe_s) < 3 or len(groupe_o) < 3:
            return 0.0, 1.0
        
        try:
            statistic, p_value = stats.mannwhitneyu(
                groupe_s, groupe_o, 
                alternative='two-sided'
            )
            return statistic, p_value
        except Exception:
            return 0.0, 1.0
    
    @staticmethod
    def test_kolmogorov_smirnov(groupe_s: List[float], groupe_o: List[float]) -> Tuple[float, float]:
        """
        Test de Kolmogorov-Smirnov pour comparer les distributions.
        
        Returns:
            Tuple[float, float]: (statistique KS, p-value)
        """
        if len(groupe_s) < 3 or len(groupe_o) < 3:
            return 0.0, 1.0
        
        try:
            statistic, p_value = stats.ks_2samp(groupe_s, groupe_o)
            return statistic, p_value
        except Exception:
            return 0.0, 1.0
    
    @staticmethod
    def effet_size_cohen_d(groupe_s: List[float], groupe_o: List[float]) -> float:
        """
        Calcule l'effet size de Cohen's d.
        
        Returns:
            float: Effet size (0.2=petit, 0.5=moyen, 0.8=grand)
        """
        if len(groupe_s) < 2 or len(groupe_o) < 2:
            return 0.0
        
        try:
            mean_s = np.mean(groupe_s)
            mean_o = np.mean(groupe_o)
            std_s = np.std(groupe_s, ddof=1)
            std_o = np.std(groupe_o, ddof=1)
            
            # Écart-type poolé
            n_s, n_o = len(groupe_s), len(groupe_o)
            pooled_std = math.sqrt(((n_s - 1) * std_s**2 + (n_o - 1) * std_o**2) / (n_s + n_o - 2))
            
            if pooled_std == 0:
                return 0.0
            
            return (mean_s - mean_o) / pooled_std
        except Exception:
            return 0.0
    
    @staticmethod
    def test_chi2_independance(groupe_s: List[float], groupe_o: List[float], nb_bins: int = 5) -> Tuple[float, float]:
        """
        Test du Chi-2 d'indépendance après discrétisation.
        
        Returns:
            Tuple[float, float]: (statistique Chi-2, p-value)
        """
        if len(groupe_s) < 10 or len(groupe_o) < 10:
            return 0.0, 1.0
        
        try:
            # Discrétisation des valeurs
            all_values = groupe_s + groupe_o
            bins = np.percentile(all_values, np.linspace(0, 100, nb_bins + 1))
            
            # Éviter les bins identiques
            bins = np.unique(bins)
            if len(bins) < 3:
                return 0.0, 1.0
            
            # Comptage par bin
            hist_s, _ = np.histogram(groupe_s, bins=bins)
            hist_o, _ = np.histogram(groupe_o, bins=bins)
            
            # Table de contingence
            contingency = np.array([hist_s, hist_o])
            
            # Test Chi-2
            chi2, p_value, _, _ = stats.chi2_contingency(contingency)
            return chi2, p_value
        except Exception:
            return 0.0, 1.0


class AnalyseurSignificanceMetriques:
    """
    Classe principale pour l'analyse de significance des métriques.
    
    Responsabilités:
    - Identifier les métriques discriminantes pour S/O
    - Effectuer des tests statistiques robustes
    - Calculer les effet sizes et intervalles de confiance
    - Classer les métriques par importance
    """
    
    def __init__(self, config):
        self.config = config
        self.tests = TestsStatistiques()
        self.resultats_significance = {}
        self.analyses_distribution = {}
        self.seuil_significance = 0.05  # Seuil p-value pour significance
        self.seuil_effet_size = 0.2     # Seuil minimum pour effet size
        
    def analyser_significance(self, donnees_analysees: List) -> bool:
        """
        Analyse la significance de toutes les métriques.
        
        Args:
            donnees_analysees: Liste des données main par main
            
        Returns:
            bool: True si l'analyse réussit, False sinon
        """
        if not donnees_analysees:
            print("ERREUR: Aucune donnée à analyser")
            return False
        
        print(f"Analyse de significance pour {len(donnees_analysees)} mains...")
        
        try:
            # Filtrer les données S/O uniquement
            donnees_so = [d for d in donnees_analysees if d.pattern in ['S', 'O']]
            
            if len(donnees_so) < 100:
                print("ATTENTION: Échantillon trop petit pour analyse statistique robuste")
            
            print(f"Données S/O: {len(donnees_so)} mains")
            
            # Séparer les groupes S et O
            donnees_s = [d for d in donnees_so if d.pattern == 'S']
            donnees_o = [d for d in donnees_so if d.pattern == 'O']
            
            print(f"Pattern S: {len(donnees_s)} mains ({len(donnees_s)/len(donnees_so)*100:.1f}%)")
            print(f"Pattern O: {len(donnees_o)} mains ({len(donnees_o)/len(donnees_so)*100:.1f}%)")
            
            # Analyser toutes les métriques disponibles
            metriques_a_analyser = self._identifier_metriques_disponibles(donnees_so)
            print(f"Métriques à analyser: {len(metriques_a_analyser)}")
            
            # Analyse métrique par métrique
            nb_significatives = 0
            for nom_metrique in metriques_a_analyser:
                resultat = self._analyser_metrique(nom_metrique, donnees_s, donnees_o)
                self.resultats_significance[nom_metrique] = resultat
                
                if resultat.significatif:
                    nb_significatives += 1
            
            print(f"Métriques significatives identifiées: {nb_significatives}/{len(metriques_a_analyser)}")
            
            # Générer les analyses de distribution
            self._generer_analyses_distribution(donnees_s, donnees_o, metriques_a_analyser)
            
            # Classer les résultats par importance
            self._classer_resultats_par_importance()
            
            return True
            
        except Exception as e:
            print(f"ERREUR lors de l'analyse de significance: {e}")
            return False
    
    def _identifier_metriques_disponibles(self, donnees_so: List) -> List[str]:
        """
        Identifie toutes les métriques disponibles dans les données.
        
        Args:
            donnees_so: Données filtrées S/O
            
        Returns:
            List[str]: Liste des noms de métriques
        """
        metriques = set()
        
        # Métriques de base
        metriques.update(['ratio_l4', 'ratio_l5', 'diff', 'diff_l4', 'diff_l5', 'entropie_globale'])
        
        # Métriques avancées depuis les données
        for donnee in donnees_so[:100]:  # Échantillon pour identifier les métriques
            if donnee.metriques_avancees:
                metriques.update(donnee.metriques_avancees.keys())
        
        return sorted(list(metriques))
    
    def _analyser_metrique(self, nom_metrique: str, donnees_s: List, donnees_o: List) -> ResultatSignificance:
        """
        Analyse la significance d'une métrique spécifique.
        
        Args:
            nom_metrique: Nom de la métrique à analyser
            donnees_s: Données pattern S
            donnees_o: Données pattern O
            
        Returns:
            ResultatSignificance: Résultat de l'analyse
        """
        # Extraction des valeurs de la métrique
        valeurs_s = self._extraire_valeurs_metrique(nom_metrique, donnees_s)
        valeurs_o = self._extraire_valeurs_metrique(nom_metrique, donnees_o)
        
        # Filtrage des valeurs valides
        valeurs_s = [v for v in valeurs_s if not (math.isnan(v) or math.isinf(v))]
        valeurs_o = [v for v in valeurs_o if not (math.isnan(v) or math.isinf(v))]
        
        if len(valeurs_s) < 3 or len(valeurs_o) < 3:
            return self._resultat_non_significatif(nom_metrique, "Échantillon insuffisant")
        
        # Tests statistiques multiples
        resultats_tests = {}
        
        # 1. Test de Mann-Whitney (principal)
        u_stat, p_mann_whitney = self.tests.test_mann_whitney(valeurs_s, valeurs_o)
        resultats_tests['mann_whitney'] = (u_stat, p_mann_whitney)
        
        # 2. Test de Kolmogorov-Smirnov
        ks_stat, p_ks = self.tests.test_kolmogorov_smirnov(valeurs_s, valeurs_o)
        resultats_tests['kolmogorov_smirnov'] = (ks_stat, p_ks)
        
        # 3. Test Chi-2 d'indépendance
        chi2_stat, p_chi2 = self.tests.test_chi2_independance(valeurs_s, valeurs_o)
        resultats_tests['chi2'] = (chi2_stat, p_chi2)
        
        # 4. Effet size
        effet_size = self.tests.effet_size_cohen_d(valeurs_s, valeurs_o)
        
        # Sélection du test principal (Mann-Whitney)
        test_principal = 'mann_whitney'
        valeur_test = u_stat
        p_value = p_mann_whitney
        
        # Détermination de la significance
        significatif = (p_value < self.seuil_significance and 
                       abs(effet_size) > self.seuil_effet_size)
        
        # Calcul des statistiques descriptives
        moyennes_par_pattern = {
            'S': np.mean(valeurs_s),
            'O': np.mean(valeurs_o)
        }
        
        ecarts_types_par_pattern = {
            'S': np.std(valeurs_s),
            'O': np.std(valeurs_o)
        }
        
        # Interprétation
        interpretation = self._interpreter_resultat(
            nom_metrique, p_value, effet_size, moyennes_par_pattern
        )
        
        return ResultatSignificance(
            nom_metrique=nom_metrique,
            test_statistique=test_principal,
            valeur_test=valeur_test,
            p_value=p_value,
            significatif=significatif,
            effet_size=effet_size,
            interpretation=interpretation,
            moyennes_par_pattern=moyennes_par_pattern,
            ecarts_types_par_pattern=ecarts_types_par_pattern
        )
    
    def _extraire_valeurs_metrique(self, nom_metrique: str, donnees: List) -> List[float]:
        """
        Extrait les valeurs d'une métrique depuis les données.
        
        Args:
            nom_metrique: Nom de la métrique
            donnees: Liste des données
            
        Returns:
            List[float]: Valeurs de la métrique
        """
        valeurs = []
        
        for donnee in donnees:
            # Métriques de base
            if nom_metrique == 'ratio_l4':
                valeurs.append(donnee.ratio_l4)
            elif nom_metrique == 'ratio_l5':
                valeurs.append(donnee.ratio_l5)
            elif nom_metrique == 'diff':
                valeurs.append(donnee.diff)
            elif nom_metrique == 'diff_l4':
                valeurs.append(donnee.diff_l4)
            elif nom_metrique == 'diff_l5':
                valeurs.append(donnee.diff_l5)
            elif nom_metrique == 'entropie_globale':
                valeurs.append(donnee.entropie_globale)
            # Métriques avancées
            elif donnee.metriques_avancees and nom_metrique in donnee.metriques_avancees:
                valeurs.append(donnee.metriques_avancees[nom_metrique])
            else:
                valeurs.append(0.0)  # Valeur par défaut
        
        return valeurs
    
    def _resultat_non_significatif(self, nom_metrique: str, raison: str) -> ResultatSignificance:
        """Crée un résultat non significatif."""
        return ResultatSignificance(
            nom_metrique=nom_metrique,
            test_statistique="aucun",
            valeur_test=0.0,
            p_value=1.0,
            significatif=False,
            effet_size=0.0,
            interpretation=f"Non significatif: {raison}",
            moyennes_par_pattern={'S': 0.0, 'O': 0.0},
            ecarts_types_par_pattern={'S': 0.0, 'O': 0.0}
        )
    
    def _interpreter_resultat(self, nom_metrique: str, p_value: float, 
                            effet_size: float, moyennes: Dict[str, float]) -> str:
        """
        Interprète le résultat statistique.
        
        Args:
            nom_metrique: Nom de la métrique
            p_value: P-value du test
            effet_size: Taille de l'effet
            moyennes: Moyennes par pattern
            
        Returns:
            str: Interprétation textuelle
        """
        if p_value >= self.seuil_significance:
            return f"Non significatif (p={p_value:.4f})"
        
        # Classification de l'effet size
        if abs(effet_size) < 0.2:
            taille_effet = "négligeable"
        elif abs(effet_size) < 0.5:
            taille_effet = "petit"
        elif abs(effet_size) < 0.8:
            taille_effet = "moyen"
        else:
            taille_effet = "grand"
        
        # Direction de l'effet
        if moyennes['S'] > moyennes['O']:
            direction = "S > O"
        else:
            direction = "O > S"
        
        return f"Significatif (p={p_value:.4f}, effet {taille_effet}, {direction})"
    
    def _generer_analyses_distribution(self, donnees_s: List, donnees_o: List, 
                                     metriques: List[str]):
        """Génère les analyses de distribution pour chaque métrique."""
        for nom_metrique in metriques:
            valeurs_s = self._extraire_valeurs_metrique(nom_metrique, donnees_s)
            valeurs_o = self._extraire_valeurs_metrique(nom_metrique, donnees_o)
            
            # Filtrage des valeurs valides
            valeurs_s = [v for v in valeurs_s if not (math.isnan(v) or math.isinf(v))]
            valeurs_o = [v for v in valeurs_o if not (math.isnan(v) or math.isinf(v))]
            
            if len(valeurs_s) >= 3:
                self.analyses_distribution[f"{nom_metrique}_S"] = self._analyser_distribution(valeurs_s, 'S')
            
            if len(valeurs_o) >= 3:
                self.analyses_distribution[f"{nom_metrique}_O"] = self._analyser_distribution(valeurs_o, 'O')
    
    def _analyser_distribution(self, valeurs: List[float], pattern: str) -> AnalyseDistribution:
        """Analyse la distribution d'une métrique pour un pattern."""
        return AnalyseDistribution(
            pattern=pattern,
            moyenne=np.mean(valeurs),
            mediane=np.median(valeurs),
            ecart_type=np.std(valeurs),
            quartile_25=np.percentile(valeurs, 25),
            quartile_75=np.percentile(valeurs, 75),
            min_val=np.min(valeurs),
            max_val=np.max(valeurs),
            nb_observations=len(valeurs)
        )
    
    def _classer_resultats_par_importance(self):
        """Classe les résultats par ordre d'importance décroissante."""
        # Tri par significance puis par effet size
        resultats_tries = sorted(
            self.resultats_significance.items(),
            key=lambda x: (x[1].significatif, abs(x[1].effet_size), -x[1].p_value),
            reverse=True
        )
        
        # Reconstruction du dictionnaire ordonné
        self.resultats_significance = dict(resultats_tries)
    
    def obtenir_metriques_significatives(self, top_n: int = None) -> List[ResultatSignificance]:
        """
        Retourne les métriques significatives classées par importance.
        
        Args:
            top_n: Nombre maximum de métriques à retourner
            
        Returns:
            List[ResultatSignificance]: Métriques significatives
        """
        significatives = [r for r in self.resultats_significance.values() if r.significatif]
        
        if top_n:
            return significatives[:top_n]
        
        return significatives
    
    def obtenir_resume_significance(self) -> Dict[str, Any]:
        """
        Retourne un résumé de l'analyse de significance.
        
        Returns:
            Dict[str, Any]: Résumé de l'analyse
        """
        total_metriques = len(self.resultats_significance)
        significatives = len([r for r in self.resultats_significance.values() if r.significatif])
        
        # Effet sizes moyens
        effet_sizes = [abs(r.effet_size) for r in self.resultats_significance.values() if r.significatif]
        effet_size_moyen = np.mean(effet_sizes) if effet_sizes else 0.0
        
        return {
            'total_metriques_analysees': total_metriques,
            'metriques_significatives': significatives,
            'pourcentage_significatives': (significatives / total_metriques * 100) if total_metriques > 0 else 0,
            'effet_size_moyen': effet_size_moyen,
            'seuil_p_value': self.seuil_significance,
            'seuil_effet_size': self.seuil_effet_size
        }
