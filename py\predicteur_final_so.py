#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Prédicteur Final S/O - Basé sur le Tableau Prédictif Complet
SYSTÈME PRÉDICTIF OPÉRATIONNEL

Ce prédicteur utilise toutes les conditions identifiées dans notre tableau
prédictif final pour prédire S (continuation) ou O (alternance) en temps réel.

Basé sur l'analyse de 100,000 parties et 5,448,036 points de données.

Auteur: Expert Statisticien
Date: 2025-06-23
"""

import sys
import os
from dataclasses import dataclass
from typing import List, Dict, Optional, Tuple
from datetime import datetime

@dataclass
class ConditionPredictive:
    """Représente une condition prédictive du tableau"""
    nom: str
    condition_func: callable
    probabilite: float
    cas_valides: int
    pattern_predit: str  # 'S' ou 'O'
    force: str  # 'FORTE', 'MODÉRÉE', 'FAIBLE'
    source: str  # 'DIFF', 'STANDARD'

@dataclass
class EtatMain:
    """État d'une main pour la prédiction"""
    ratio_l4: float
    ratio_l5: float
    diff_l4: float  # Variation L4 par rapport à main précédente
    diff_l5: float  # Variation L5 par rapport à main précédente
    diff: float     # |L4-L5| cohérence
    main_numero: int

@dataclass
class ResultatPrediction:
    """Résultat d'une prédiction"""
    prediction: str  # 'S', 'O', ou 'ABSTENTION'
    probabilite: float
    confiance: str  # 'TRÈS_ÉLEVÉE', 'ÉLEVÉE', 'MODÉRÉE', 'FAIBLE'
    conditions_activees: List[str]
    scores_detailles: Dict[str, float]
    justification: str
    nb_conditions_s: int
    nb_conditions_o: int

class PredicteurFinalSO:
    """
    Prédicteur final S/O basé sur le tableau prédictif complet
    """
    
    def __init__(self):
        """Initialise le prédicteur avec toutes les conditions du tableau"""
        self.conditions_s = self._charger_conditions_s()
        self.conditions_o = self._charger_conditions_o()
        self.seuil_confiance_min = 55.0  # Seuil minimum pour prédiction
        self.seuil_abstention = 52.0     # En dessous = abstention
        
        print("🎯 PRÉDICTEUR FINAL S/O INITIALISÉ")
        print(f"✅ {len(self.conditions_s)} conditions S chargées")
        print(f"✅ {len(self.conditions_o)} conditions O chargées")
    
    def _charger_conditions_s(self) -> List[ConditionPredictive]:
        """Charge toutes les conditions qui prédisent S"""
        return [
            # CONDITIONS S EXCEPTIONNELLES (≥70%)
            ConditionPredictive(
                "INCOH_TRÈS_INCOHÉRENT", 
                lambda e: abs(e.ratio_l4 - e.ratio_l5) > 0.5,
                98.4, 122, 'S', 'EXCEPTIONNELLE', 'STANDARD'
            ),
            ConditionPredictive(
                "DIFF_SIGNAL_INUTILISABLE", 
                lambda e: e.diff > 0.300,
                78.2, 4138, 'S', 'EXCEPTIONNELLE', 'DIFF'
            ),
            ConditionPredictive(
                "COMPLEX_INCOH_FORTE_VAR", 
                lambda e: abs(e.ratio_l4 - e.ratio_l5) > 0.2 and (e.diff_l4 > 0.1 or e.diff_l5 > 0.1),
                75.9, 43120, 'S', 'TRÈS_FORTE', 'STANDARD'
            ),
            ConditionPredictive(
                "COMB_VARIATIONS_FORTES_DIFF_DOUTEUX", 
                lambda e: (e.diff_l4 > 0.1 or e.diff_l5 > 0.1) and e.diff > 0.150,
                72.1, 276323, 'S', 'TRÈS_FORTE', 'DIFF'
            ),
            ConditionPredictive(
                "INCOH_INCOHÉRENT", 
                lambda e: 0.2 <= abs(e.ratio_l4 - e.ratio_l5) < 0.5,
                72.1, 48025, 'S', 'TRÈS_FORTE', 'STANDARD'
            ),
            ConditionPredictive(
                "DIFF_SIGNAL_TRÈS_DOUTEUX", 
                lambda e: 0.200 <= e.diff < 0.300,
                71.6, 44009, 'S', 'TRÈS_FORTE', 'DIFF'
            ),
            ConditionPredictive(
                "COMB_ORDRE_FORT_DIFF_DOUTEUX", 
                lambda e: e.ratio_l4 < 0.5 and e.diff > 0.150,
                70.5, 223501, 'S', 'TRÈS_FORTE', 'DIFF'
            ),
            
            # CONDITIONS S FORTES (60-70%)
            ConditionPredictive(
                "L5_ORDRE_TRÈS_FORT", 
                lambda e: e.ratio_l5 < 0.3,
                68.8, 96873, 'S', 'FORTE', 'STANDARD'
            ),
            ConditionPredictive(
                "L4_ORDRE_TRÈS_FORT", 
                lambda e: e.ratio_l4 < 0.3,
                68.5, 334525, 'S', 'FORTE', 'STANDARD'
            ),
            ConditionPredictive(
                "COMB_ORDRE_FORT_DIFF_EXCELLENT", 
                lambda e: e.ratio_l4 < 0.5 and 0.020 <= e.diff < 0.030,
                68.0, 28193, 'S', 'FORTE', 'DIFF'
            ),
            ConditionPredictive(
                "COMPLEX_ORDRE_FORT_VARIABLE", 
                lambda e: e.ratio_l4 < 0.5 and e.diff_l4 > 0.1,
                65.8, 1054338, 'S', 'FORTE', 'STANDARD'
            ),
            ConditionPredictive(
                "COMB_ORDRE_CHAOS", 
                lambda e: e.ratio_l4 < 0.7 and e.ratio_l5 > 0.9,
                65.4, 697, 'S', 'FORTE', 'STANDARD'
            ),
            ConditionPredictive(
                "DIFF_L5_TRÈS_FORTE_VAR", 
                lambda e: 0.2 <= e.diff_l5 < 0.5,
                64.8, 41005, 'S', 'FORTE', 'STANDARD'
            ),
            ConditionPredictive(
                "DIFF_L4_TRÈS_FORTE_VAR", 
                lambda e: 0.2 <= e.diff_l4 < 0.5,
                64.1, 166976, 'S', 'FORTE', 'STANDARD'
            ),
            ConditionPredictive(
                "DIFF_SIGNAL_DOUTEUX", 
                lambda e: 0.150 <= e.diff < 0.200,
                64.1, 310756, 'S', 'FORTE', 'DIFF'
            ),
            ConditionPredictive(
                "DIFF_L4_VAR_EXTRÊME", 
                lambda e: e.diff_l4 > 0.5,
                63.6, 393, 'S', 'FORTE', 'STANDARD'
            ),
            ConditionPredictive(
                "DIFF_L5_VAR_EXTRÊME", 
                lambda e: e.diff_l5 > 0.5,
                68.6, 153, 'S', 'FORTE', 'STANDARD'
            ),
            
            # CONDITIONS S MODÉRÉES (55-60%)
            ConditionPredictive(
                "DIFF_L4_FORTE_VAR", 
                lambda e: 0.1 <= e.diff_l4 < 0.2,
                57.5, 1702763, 'S', 'MODÉRÉE', 'STANDARD'
            ),
            ConditionPredictive(
                "L5_ORDRE_FORT", 
                lambda e: 0.3 <= e.ratio_l5 < 0.5,
                56.9, 806765, 'S', 'MODÉRÉE', 'STANDARD'
            ),
            ConditionPredictive(
                "DIFF_L5_FORTE_VAR", 
                lambda e: 0.1 <= e.diff_l5 < 0.2,
                55.8, 1992317, 'S', 'MODÉRÉE', 'STANDARD'
            ),
            ConditionPredictive(
                "L4_ORDRE_FORT", 
                lambda e: 0.3 <= e.ratio_l4 < 0.5,
                55.6, 1727166, 'S', 'MODÉRÉE', 'STANDARD'
            ),
            ConditionPredictive(
                "INCOH_PEU_COHÉRENT", 
                lambda e: 0.1 <= abs(e.ratio_l4 - e.ratio_l5) < 0.2,
                55.4, 2110687, 'S', 'MODÉRÉE', 'STANDARD'
            )
        ]
    
    def _charger_conditions_o(self) -> List[ConditionPredictive]:
        """Charge toutes les conditions qui prédisent O"""
        return [
            # CONDITIONS O MODÉRÉES (≥55%)
            ConditionPredictive(
                "COMPLEX_CHAOS_VARIABLE", 
                lambda e: e.ratio_l4 > 1.0 and e.diff_l4 > 0.1,
                56.2, 1859, 'O', 'MODÉRÉE', 'STANDARD'
            ),
            ConditionPredictive(
                "DIFF_SIGNAL_ACCEPTABLE", 
                lambda e: 0.075 <= e.diff < 0.100,
                55.9, 1772432, 'O', 'MODÉRÉE', 'DIFF'
            ),
            ConditionPredictive(
                "INCOH_MODÉRÉMENT_COHÉRENT", 
                lambda e: 0.05 <= abs(e.ratio_l4 - e.ratio_l5) < 0.1,
                55.7, 1796892, 'O', 'MODÉRÉE', 'STANDARD'
            ),
            ConditionPredictive(
                "DIFF_L4_FAIBLE_VAR", 
                lambda e: 0.02 <= e.diff_l4 < 0.05,
                55.5, 296476, 'O', 'MODÉRÉE', 'STANDARD'
            ),
            ConditionPredictive(
                "COMB_CHAOS_DIFF_DOUTEUX", 
                lambda e: e.ratio_l4 > 0.9 and e.diff > 0.150,
                55.4, 2983, 'O', 'MODÉRÉE', 'DIFF'
            ),
            ConditionPredictive(
                "COMPLEX_CHAOS_STABLE", 
                lambda e: e.ratio_l4 > 1.0 and e.diff_l4 < 0.02,
                55.3, 844, 'O', 'MODÉRÉE', 'STANDARD'
            ),
            ConditionPredictive(
                "COMB_CHAOS_CHAOS", 
                lambda e: e.ratio_l4 > 0.9 and e.ratio_l5 > 0.9,
                55.2, 15226, 'O', 'MODÉRÉE', 'STANDARD'
            ),
            ConditionPredictive(
                "DIFF_L4_STABLE", 
                lambda e: 0.01 <= e.diff_l4 < 0.02,
                55.2, 231445, 'O', 'MODÉRÉE', 'STANDARD'
            ),
            ConditionPredictive(
                "COMB_STABILITÉ_DIFF_EXCELLENT", 
                lambda e: e.diff_l4 < 0.02 and e.diff_l5 < 0.02 and 0.020 <= e.diff < 0.030,
                55.1, 75754, 'O', 'MODÉRÉE', 'DIFF'
            ),
            ConditionPredictive(
                "COMB_ÉQUILIBRE_ÉQUILIBRE", 
                lambda e: 0.7 <= e.ratio_l4 <= 0.9 and 0.7 <= e.ratio_l5 <= 0.9,
                55.1, 177335, 'O', 'MODÉRÉE', 'STANDARD'
            ),
            ConditionPredictive(
                "L4_CHAOS_MODÉRÉ", 
                lambda e: 0.9 <= e.ratio_l4 < 1.1,
                55.0, 18924, 'O', 'MODÉRÉE', 'STANDARD'
            ),
            
            # CONDITIONS O FAIBLES (52-55%)
            ConditionPredictive(
                "L4_CHAOS_FORT", 
                lambda e: 1.1 <= e.ratio_l4 < 1.5,
                54.7, 1278, 'O', 'FAIBLE', 'STANDARD'
            ),
            ConditionPredictive(
                "L4_ORDRE_MODÉRÉ", 
                lambda e: 0.5 <= e.ratio_l4 < 0.7,
                54.6, 3104273, 'O', 'FAIBLE', 'STANDARD'
            ),
            ConditionPredictive(
                "DIFF_L5_STABLE", 
                lambda e: 0.01 <= e.diff_l5 < 0.02,
                54.5, 202508, 'O', 'FAIBLE', 'STANDARD'
            ),
            ConditionPredictive(
                "DIFF_L4_TRÈS_STABLE", 
                lambda e: e.diff_l4 < 0.01,
                54.2, 2903989, 'O', 'FAIBLE', 'STANDARD'
            ),
            ConditionPredictive(
                "DIFF_L5_VAR_MODÉRÉE", 
                lambda e: 0.05 <= e.diff_l5 < 0.1,
                54.2, 270870, 'O', 'FAIBLE', 'STANDARD'
            ),
            ConditionPredictive(
                "L5_CHAOS_MODÉRÉ", 
                lambda e: 0.9 <= e.ratio_l5 < 1.1,
                54.1, 73339, 'O', 'FAIBLE', 'STANDARD'
            ),
            ConditionPredictive(
                "DIFF_SIGNAL_EXCELLENT", 
                lambda e: 0.020 <= e.diff < 0.030,
                54.0, 846057, 'O', 'FAIBLE', 'DIFF'
            ),
            ConditionPredictive(
                "L5_ÉQUILIBRE", 
                lambda e: 0.7 <= e.ratio_l5 <= 0.9,
                53.7, 698750, 'O', 'FAIBLE', 'STANDARD'
            ),
            ConditionPredictive(
                "DIFF_L5_TRÈS_STABLE", 
                lambda e: e.diff_l5 < 0.01,
                53.7, 2592849, 'O', 'FAIBLE', 'STANDARD'
            )
        ]
    
    def predire(self, etat_main: EtatMain) -> ResultatPrediction:
        """
        Effectue une prédiction S/O basée sur l'état de la main
        """
        # Évaluer toutes les conditions S
        conditions_s_activees = []
        scores_s = []
        
        for condition in self.conditions_s:
            try:
                if condition.condition_func(etat_main):
                    conditions_s_activees.append(condition.nom)
                    scores_s.append(condition.probabilite)
            except:
                continue  # Ignorer les erreurs d'évaluation
        
        # Évaluer toutes les conditions O
        conditions_o_activees = []
        scores_o = []
        
        for condition in self.conditions_o:
            try:
                if condition.condition_func(etat_main):
                    conditions_o_activees.append(condition.nom)
                    scores_o.append(condition.probabilite)
            except:
                continue  # Ignorer les erreurs d'évaluation
        
        # Calculer les scores moyens pondérés
        score_s = self._calculer_score_pondere(scores_s) if scores_s else 50.0
        score_o = self._calculer_score_pondere(scores_o) if scores_o else 50.0
        
        # Déterminer la prédiction
        if score_s > score_o and score_s >= self.seuil_confiance_min:
            prediction = 'S'
            probabilite = score_s
            conditions_activees = conditions_s_activees
        elif score_o > score_s and score_o >= self.seuil_confiance_min:
            prediction = 'O'
            probabilite = score_o
            conditions_activees = conditions_o_activees
        else:
            prediction = 'ABSTENTION'
            probabilite = max(score_s, score_o)
            conditions_activees = []
        
        # Déterminer le niveau de confiance
        confiance = self._determiner_confiance(probabilite, len(conditions_activees))
        
        # Générer la justification
        justification = self._generer_justification(
            prediction, probabilite, len(conditions_s_activees), len(conditions_o_activees)
        )
        
        return ResultatPrediction(
            prediction=prediction,
            probabilite=probabilite,
            confiance=confiance,
            conditions_activees=conditions_activees,
            scores_detailles={'score_s': score_s, 'score_o': score_o},
            justification=justification,
            nb_conditions_s=len(conditions_s_activees),
            nb_conditions_o=len(conditions_o_activees)
        )
    
    def _calculer_score_pondere(self, scores: List[float]) -> float:
        """Calcule un score pondéré basé sur les probabilités"""
        if not scores:
            return 50.0
        
        # Pondération : plus de poids aux scores élevés
        scores_ponderes = []
        for score in scores:
            if score >= 70:
                poids = 3.0  # Poids élevé pour conditions exceptionnelles
            elif score >= 60:
                poids = 2.0  # Poids moyen pour conditions fortes
            else:
                poids = 1.0  # Poids normal pour conditions modérées
            
            scores_ponderes.append(score * poids)
        
        # Moyenne pondérée
        return sum(scores_ponderes) / len(scores_ponderes)
    
    def _determiner_confiance(self, probabilite: float, nb_conditions: int) -> str:
        """Détermine le niveau de confiance"""
        if probabilite >= 75 and nb_conditions >= 2:
            return 'TRÈS_ÉLEVÉE'
        elif probabilite >= 65 and nb_conditions >= 1:
            return 'ÉLEVÉE'
        elif probabilite >= 55:
            return 'MODÉRÉE'
        else:
            return 'FAIBLE'
    
    def _generer_justification(self, prediction: str, probabilite: float, 
                             nb_s: int, nb_o: int) -> str:
        """Génère une justification pour la prédiction"""
        if prediction == 'ABSTENTION':
            return f"Abstention recommandée - Signaux contradictoires ({nb_s} S, {nb_o} O)"
        elif prediction == 'S':
            return f"Prédiction S avec {probabilite:.1f}% de probabilité ({nb_s} conditions S activées)"
        else:
            return f"Prédiction O avec {probabilite:.1f}% de probabilité ({nb_o} conditions O activées)"

def tester_predicteur():
    """Test du prédicteur avec des exemples"""
    print("🧪 TEST DU PRÉDICTEUR FINAL S/O")
    print("=" * 50)
    
    predicteur = PredicteurFinalSO()
    
    # Cas de test
    cas_tests = [
        # Cas favorable à S (incohérence forte)
        EtatMain(0.2, 0.8, 0.15, 0.12, 0.6, 10),
        # Cas favorable à O (équilibre)
        EtatMain(0.8, 0.85, 0.01, 0.015, 0.05, 11),
        # Cas d'abstention (signaux mixtes)
        EtatMain(0.6, 0.65, 0.08, 0.07, 0.05, 12),
        # Cas extrême S (ordre très fort + variations)
        EtatMain(0.1, 0.2, 0.25, 0.3, 0.1, 13),
        # Cas chaos modéré O
        EtatMain(1.2, 1.1, 0.02, 0.015, 0.1, 14)
    ]
    
    for i, etat in enumerate(cas_tests, 1):
        print(f"\n📊 CAS TEST {i}:")
        print(f"   L4={etat.ratio_l4:.3f}, L5={etat.ratio_l5:.3f}")
        print(f"   DIFF_L4={etat.diff_l4:.3f}, DIFF_L5={etat.diff_l5:.3f}")
        print(f"   DIFF={etat.diff:.3f}")
        
        resultat = predicteur.predire(etat)
        
        print(f"   🎯 PRÉDICTION: {resultat.prediction}")
        print(f"   📈 PROBABILITÉ: {resultat.probabilite:.1f}%")
        print(f"   🔒 CONFIANCE: {resultat.confiance}")
        print(f"   📋 CONDITIONS: {len(resultat.conditions_activees)}")
        print(f"   💬 JUSTIFICATION: {resultat.justification}")

if __name__ == "__main__":
    print("🚀 PRÉDICTEUR FINAL S/O - SYSTÈME OPÉRATIONNEL")
    print("=" * 70)
    
    # Test du prédicteur
    tester_predicteur()
    
    print(f"\n🎯 PRÉDICTEUR FINAL CRÉÉ ET TESTÉ !")
    print("✅ Basé sur tableau prédictif complet")
    print("📊 22 conditions S + 20 conditions O")
    print("🚀 Système prédictif opérationnel")
    
    print("\n" + "=" * 70)
