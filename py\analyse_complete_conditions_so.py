#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Analyse Complète des Conditions Prédictives S/O
TABLEAU EXHAUSTIF DES CONDITIONS POUR PRÉDIRE S ET O

Ce script analyse les 100,000 parties pour identifier toutes les conditions
qui permettent de prédire les patterns S (continuation) ou O (alternance).

Auteur: Expert Statisticien
Date: 2025-06-23
"""

import sys
import os
import numpy as np
from datetime import datetime
from collections import defaultdict

# Ajouter le répertoire courant au path pour les imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def analyser_conditions_predictives_so():
    """
    Analyse complète des conditions prédictives pour S et O
    """
    print("🔬 ANALYSE COMPLÈTE CONDITIONS PRÉDICTIVES S/O")
    print("📊 Recherche exhaustive sur 100,000 parties")
    print("=" * 60)
    
    try:
        # Import des modules d'analyse
        from analyseur_transitions_index5 import AnalyseurEvolutionEntropique, AnalyseurEvolutionRatios
        
        print("✅ Import des modules réussi")
        
        # PHASE 1: Chargement et analyse des données
        print(f"\n📊 PHASE 1: CHARGEMENT DONNÉES 100,000 PARTIES")
        print("-" * 50)
        
        dataset_path = "dataset_baccarat_lupasco_20250622_011427.json"
        if not os.path.exists(dataset_path):
            print(f"❌ Dataset non trouvé: {dataset_path}")
            return False
        
        print("🔄 Chargement analyseur entropique...")
        analyseur_entropique = AnalyseurEvolutionEntropique(dataset_path)
        
        # Vérifier si l'analyse est déjà faite
        if not hasattr(analyseur_entropique, 'evolutions_entropiques') or not analyseur_entropique.evolutions_entropiques:
            print("🔄 Analyse entropique en cours...")
            resultats_entropiques = analyseur_entropique.analyser_toutes_parties_entropiques(nb_parties_max=100000)
            print(f"✅ {resultats_entropiques['parties_reussies']:,} parties analysées")
        else:
            print(f"✅ Données entropiques déjà disponibles")
        
        print("🔄 Chargement analyseur ratios...")
        analyseur_ratios = AnalyseurEvolutionRatios(analyseur_entropique)
        
        if not hasattr(analyseur_ratios, 'evolutions_ratios') or not analyseur_ratios.evolutions_ratios:
            print("🔄 Analyse ratios en cours...")
            analyseur_ratios.analyser_evolution_toutes_parties()
            print(f"✅ Ratios calculés pour {len(analyseur_ratios.evolutions_ratios):,} parties")
        else:
            print(f"✅ Données ratios déjà disponibles")
        
        # PHASE 2: Extraction des données pour analyse
        print(f"\n📊 PHASE 2: EXTRACTION DONNÉES POUR ANALYSE")
        print("-" * 50)
        
        donnees_analyse = []
        parties_traitees = 0
        
        for partie_id, evolution_ratios in analyseur_ratios.evolutions_ratios.items():
            if 'erreur' in evolution_ratios:
                continue
            
            # Vérifier la présence de toutes les données nécessaires
            if not all(key in evolution_ratios for key in ['ratios_l4', 'ratios_l5', 'patterns_soe', 'index3_resultats']):
                continue
            
            ratios_l4 = evolution_ratios['ratios_l4']
            ratios_l5 = evolution_ratios['ratios_l5']
            patterns = evolution_ratios['patterns_soe']
            index3 = evolution_ratios['index3_resultats']
            diff_l4_vars = evolution_ratios.get('diff_l4_variations', [])
            diff_l5_vars = evolution_ratios.get('diff_l5_variations', [])
            
            # Aligner les données (patterns commence à la main 2)
            for i in range(len(patterns)):
                if i + 1 < len(ratios_l4) and i + 1 < len(ratios_l5) and i + 1 < len(index3):
                    # Données de la main où le pattern s'applique
                    ratio_l4_main = ratios_l4[i + 1]
                    ratio_l5_main = ratios_l5[i + 1]
                    pattern = patterns[i]
                    index3_main = index3[i + 1]
                    
                    # Calculer les différentiels si disponibles
                    diff_l4 = diff_l4_vars[i] if i < len(diff_l4_vars) else 0.0
                    diff_l5 = diff_l5_vars[i] if i < len(diff_l5_vars) else 0.0
                    
                    # Calculer l'incohérence L4/L5 (DIFF)
                    diff_coherence = abs(ratio_l4_main - ratio_l5_main)  # DIFF = |L4-L5|

                    # Ignorer les patterns E (TIE) pour cette analyse
                    if pattern in ['S', 'O']:
                        donnees_analyse.append({
                            'partie_id': partie_id,
                            'main': i + 7,  # Main réelle
                            'ratio_l4': ratio_l4_main,
                            'ratio_l5': ratio_l5_main,
                            'diff_l4': diff_l4,
                            'diff_l5': diff_l5,
                            'diff': diff_coherence,  # NOUVELLE VARIABLE DIFF AJOUTÉE
                            'incoherence': diff_coherence,  # Alias pour compatibilité
                            'pattern': pattern,
                            'index3': index3_main
                        })
            
            parties_traitees += 1
            if parties_traitees % 10000 == 0:
                print(f"   📊 {parties_traitees:,} parties traitées...")
        
        print(f"✅ {len(donnees_analyse):,} points de données extraits")
        
        # PHASE 3: Analyse exhaustive des conditions
        print(f"\n📊 PHASE 3: ANALYSE EXHAUSTIVE DES CONDITIONS")
        print("-" * 50)
        
        conditions_s, conditions_o = analyser_toutes_conditions(donnees_analyse)
        
        # PHASE 4: Génération du tableau prédictif
        print(f"\n📊 PHASE 4: GÉNÉRATION TABLEAU PRÉDICTIF")
        print("-" * 50)
        
        nom_rapport = generer_tableau_predictif_so(conditions_s, conditions_o, len(donnees_analyse))
        
        print(f"✅ Tableau prédictif généré: {nom_rapport}")
        
        # PHASE 5: Affichage des résultats principaux
        print(f"\n📊 PHASE 5: RÉSULTATS PRINCIPAUX")
        print("-" * 50)
        
        afficher_resultats_principaux(conditions_s, conditions_o)
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur durant l'analyse: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyser_toutes_conditions(donnees):
    """
    Analyse toutes les conditions possibles pour prédire S et O
    """
    print("🔬 Analyse exhaustive des conditions...")
    
    conditions_s = []  # Conditions qui favorisent S
    conditions_o = []  # Conditions qui favorisent O
    
    # ANALYSE 1: Ratios L4 par tranches fines
    print("   📊 Analyse ratios L4...")
    tranches_l4 = [
        (0.0, 0.3, "ORDRE_TRÈS_FORT"),
        (0.3, 0.5, "ORDRE_FORT"),
        (0.5, 0.7, "ORDRE_MODÉRÉ"),
        (0.7, 0.9, "ÉQUILIBRE"),
        (0.9, 1.1, "CHAOS_MODÉRÉ"),
        (1.1, 1.5, "CHAOS_FORT"),
        (1.5, 10.0, "CHAOS_EXTRÊME")
    ]
    
    for min_val, max_val, nom in tranches_l4:
        donnees_tranche = [d for d in donnees if min_val <= d['ratio_l4'] < max_val]
        if len(donnees_tranche) >= 100:  # Seuil minimum
            analyser_tranche(donnees_tranche, f"L4_{nom}", conditions_s, conditions_o)
    
    # ANALYSE 2: Ratios L5 par tranches fines
    print("   📊 Analyse ratios L5...")
    for min_val, max_val, nom in tranches_l4:  # Même tranches
        donnees_tranche = [d for d in donnees if min_val <= d['ratio_l5'] < max_val]
        if len(donnees_tranche) >= 100:
            analyser_tranche(donnees_tranche, f"L5_{nom}", conditions_s, conditions_o)
    
    # ANALYSE 3: Différentiels L4 par tranches
    print("   📊 Analyse différentiels L4...")
    tranches_diff = [
        (0.0, 0.01, "TRÈS_STABLE"),
        (0.01, 0.02, "STABLE"),
        (0.02, 0.05, "FAIBLE_VAR"),
        (0.05, 0.1, "VAR_MODÉRÉE"),
        (0.1, 0.2, "FORTE_VAR"),
        (0.2, 0.5, "TRÈS_FORTE_VAR"),
        (0.5, 10.0, "VAR_EXTRÊME")
    ]
    
    for min_val, max_val, nom in tranches_diff:
        donnees_tranche = [d for d in donnees if min_val <= d['diff_l4'] < max_val]
        if len(donnees_tranche) >= 100:
            analyser_tranche(donnees_tranche, f"DIFF_L4_{nom}", conditions_s, conditions_o)
    
    # ANALYSE 4: Différentiels L5 par tranches
    print("   📊 Analyse différentiels L5...")
    for min_val, max_val, nom in tranches_diff:
        donnees_tranche = [d for d in donnees if min_val <= d['diff_l5'] < max_val]
        if len(donnees_tranche) >= 100:
            analyser_tranche(donnees_tranche, f"DIFF_L5_{nom}", conditions_s, conditions_o)
    
    # ANALYSE 5: DIFF (Cohérence L4/L5) par tranches - NOUVELLE ANALYSE CRITIQUE
    print("   📊 Analyse DIFF (cohérence L4/L5)...")
    tranches_diff_coherence = [
        (0.0, 0.030, "EXCELLENT_SIGNAL"),      # Signal excellent
        (0.030, 0.050, "TRÈS_BON_SIGNAL"),     # Signal très fiable
        (0.050, 0.100, "BON_SIGNAL"),          # Signal acceptable
        (0.100, 0.150, "SIGNAL_RISQUÉ"),       # Signal risqué
        (0.150, 0.300, "SIGNAL_DOUTEUX"),      # Signal douteux
        (0.300, 10.0, "SIGNAL_TRÈS_DOUTEUX")   # Signal très douteux
    ]

    for min_val, max_val, nom in tranches_diff_coherence:
        donnees_tranche = [d for d in donnees if min_val <= d['diff'] < max_val]
        if len(donnees_tranche) >= 100:
            analyser_tranche(donnees_tranche, f"DIFF_{nom}", conditions_s, conditions_o)

    # ANALYSE 6: Incohérence L4/L5 par tranches (ancien système pour comparaison)
    print("   📊 Analyse incohérence L4/L5 (ancien)...")
    tranches_incoh = [
        (0.0, 0.02, "TRÈS_COHÉRENT"),
        (0.02, 0.05, "COHÉRENT"),
        (0.05, 0.1, "MODÉRÉMENT_COHÉRENT"),
        (0.1, 0.2, "PEU_COHÉRENT"),
        (0.2, 0.5, "INCOHÉRENT"),
        (0.5, 10.0, "TRÈS_INCOHÉRENT")
    ]

    for min_val, max_val, nom in tranches_incoh:
        donnees_tranche = [d for d in donnees if min_val <= d['incoherence'] < max_val]
        if len(donnees_tranche) >= 100:
            analyser_tranche(donnees_tranche, f"INCOH_{nom}", conditions_s, conditions_o)
    
    # ANALYSE 6: Combinaisons L4 + L5
    print("   📊 Analyse combinaisons L4+L5...")
    combinaisons = {
        "ORDRE_ORDRE": lambda d: d['ratio_l4'] < 0.7 and d['ratio_l5'] < 0.7,
        "ORDRE_CHAOS": lambda d: d['ratio_l4'] < 0.7 and d['ratio_l5'] > 0.9,
        "CHAOS_ORDRE": lambda d: d['ratio_l4'] > 0.9 and d['ratio_l5'] < 0.7,
        "CHAOS_CHAOS": lambda d: d['ratio_l4'] > 0.9 and d['ratio_l5'] > 0.9,
        "ÉQUILIBRE_ÉQUILIBRE": lambda d: 0.7 <= d['ratio_l4'] <= 0.9 and 0.7 <= d['ratio_l5'] <= 0.9
    }
    
    for nom, condition in combinaisons.items():
        donnees_comb = [d for d in donnees if condition(d)]
        if len(donnees_comb) >= 100:
            analyser_tranche(donnees_comb, f"COMB_{nom}", conditions_s, conditions_o)
    
    # ANALYSE 8: Conditions complexes avec DIFF
    print("   📊 Analyse conditions complexes avec DIFF...")
    conditions_complexes = {
        "ORDRE_FORT_STABLE": lambda d: d['ratio_l4'] < 0.5 and d['diff_l4'] < 0.02,
        "ORDRE_FORT_VARIABLE": lambda d: d['ratio_l4'] < 0.5 and d['diff_l4'] > 0.1,
        "CHAOS_STABLE": lambda d: d['ratio_l4'] > 1.0 and d['diff_l4'] < 0.02,
        "CHAOS_VARIABLE": lambda d: d['ratio_l4'] > 1.0 and d['diff_l4'] > 0.1,
        "INCOH_FORTE_VAR": lambda d: d['incoherence'] > 0.2 and (d['diff_l4'] > 0.1 or d['diff_l5'] > 0.1),
        "COHÉRENT_STABLE": lambda d: d['incoherence'] < 0.05 and d['diff_l4'] < 0.02 and d['diff_l5'] < 0.02,

        # NOUVELLES CONDITIONS AVEC DIFF
        "ORDRE_SIGNAL_EXCELLENT": lambda d: d['ratio_l4'] < 0.7 and d['diff'] < 0.030,
        "ORDRE_SIGNAL_TRÈS_BON": lambda d: d['ratio_l4'] < 0.7 and 0.030 <= d['diff'] < 0.050,
        "ORDRE_SIGNAL_BON": lambda d: d['ratio_l4'] < 0.7 and 0.050 <= d['diff'] < 0.100,
        "ORDRE_SIGNAL_RISQUÉ": lambda d: d['ratio_l4'] < 0.7 and d['diff'] >= 0.100,

        "CHAOS_SIGNAL_EXCELLENT": lambda d: d['ratio_l4'] > 0.9 and d['diff'] < 0.030,
        "CHAOS_SIGNAL_DOUTEUX": lambda d: d['ratio_l4'] > 0.9 and d['diff'] > 0.150,

        "ÉQUILIBRE_SIGNAL_EXCELLENT": lambda d: 0.7 <= d['ratio_l4'] <= 0.9 and d['diff'] < 0.030,
        "ÉQUILIBRE_SIGNAL_DOUTEUX": lambda d: 0.7 <= d['ratio_l4'] <= 0.9 and d['diff'] > 0.150,

        # CONDITIONS ULTRA-SPÉCIFIQUES AVEC DIFF
        "ORDRE_FORT_DIFF_EXCELLENT": lambda d: d['ratio_l4'] < 0.5 and d['diff'] < 0.030,
        "ORDRE_FORT_DIFF_DOUTEUX": lambda d: d['ratio_l4'] < 0.5 and d['diff'] > 0.150,
        "VARIATIONS_FORTES_DIFF_EXCELLENT": lambda d: (d['diff_l4'] > 0.1 or d['diff_l5'] > 0.1) and d['diff'] < 0.030,
        "VARIATIONS_FORTES_DIFF_DOUTEUX": lambda d: (d['diff_l4'] > 0.1 or d['diff_l5'] > 0.1) and d['diff'] > 0.150
    }
    
    for nom, condition in conditions_complexes.items():
        donnees_cond = [d for d in donnees if condition(d)]
        if len(donnees_cond) >= 100:
            analyser_tranche(donnees_cond, f"COMPLEX_{nom}", conditions_s, conditions_o)
    
    print(f"✅ Analyse terminée: {len(conditions_s)} conditions S, {len(conditions_o)} conditions O")
    
    return conditions_s, conditions_o

def analyser_tranche(donnees_tranche, nom_condition, conditions_s, conditions_o):
    """
    Analyse une tranche de données et détermine si elle favorise S ou O
    """
    if len(donnees_tranche) < 100:
        return
    
    nb_s = len([d for d in donnees_tranche if d['pattern'] == 'S'])
    nb_o = len([d for d in donnees_tranche if d['pattern'] == 'O'])
    total = nb_s + nb_o
    
    if total == 0:
        return
    
    pourcentage_s = (nb_s / total) * 100
    pourcentage_o = (nb_o / total) * 100
    
    # Seuils pour considérer une condition comme prédictive
    seuil_s = 52.0  # Au moins 52% pour S
    seuil_o = 52.0  # Au moins 52% pour O
    
    condition_data = {
        'nom': nom_condition,
        'total_cas': total,
        'nb_s': nb_s,
        'nb_o': nb_o,
        'pourcentage_s': pourcentage_s,
        'pourcentage_o': pourcentage_o,
        'force': 'FORTE' if max(pourcentage_s, pourcentage_o) >= 60 else 'MODÉRÉE' if max(pourcentage_s, pourcentage_o) >= 55 else 'FAIBLE'
    }
    
    # Ajouter aux conditions appropriées
    if pourcentage_s >= seuil_s and pourcentage_s > pourcentage_o:
        conditions_s.append(condition_data)
    elif pourcentage_o >= seuil_o and pourcentage_o > pourcentage_s:
        conditions_o.append(condition_data)

def generer_tableau_predictif_so(conditions_s, conditions_o, total_donnees):
    """
    Génère le tableau prédictif S/O
    """
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    nom_fichier = f"tableau_predictif_so_{timestamp}.txt"
    
    with open(nom_fichier, 'w', encoding='utf-8') as f:
        f.write("TABLEAU PRÉDICTIF EXHAUSTIF S/O\n")
        f.write("=" * 60 + "\n\n")
        f.write(f"Date de génération: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Données analysées: {total_donnees:,} points\n")
        f.write(f"Conditions S identifiées: {len(conditions_s)}\n")
        f.write(f"Conditions O identifiées: {len(conditions_o)}\n\n")
        
        # TABLEAU CONDITIONS S
        f.write("CONDITIONS QUI FAVORISENT S (CONTINUATION)\n")
        f.write("=" * 50 + "\n\n")
        
        # Trier par pourcentage décroissant
        conditions_s_triees = sorted(conditions_s, key=lambda x: x['pourcentage_s'], reverse=True)
        
        f.write("CONDITION                          | CAS     | %S    | %O    | FORCE\n")
        f.write("-" * 70 + "\n")
        
        for cond in conditions_s_triees:
            f.write(f"{cond['nom']:<34} | {cond['total_cas']:>6,} | {cond['pourcentage_s']:>5.1f} | {cond['pourcentage_o']:>5.1f} | {cond['force']}\n")
        
        f.write(f"\nTOTAL CONDITIONS S: {len(conditions_s_triees)}\n\n")
        
        # TABLEAU CONDITIONS O
        f.write("CONDITIONS QUI FAVORISENT O (ALTERNANCE)\n")
        f.write("=" * 50 + "\n\n")
        
        # Trier par pourcentage décroissant
        conditions_o_triees = sorted(conditions_o, key=lambda x: x['pourcentage_o'], reverse=True)
        
        f.write("CONDITION                          | CAS     | %S    | %O    | FORCE\n")
        f.write("-" * 70 + "\n")
        
        for cond in conditions_o_triees:
            f.write(f"{cond['nom']:<34} | {cond['total_cas']:>6,} | {cond['pourcentage_s']:>5.1f} | {cond['pourcentage_o']:>5.1f} | {cond['force']}\n")
        
        f.write(f"\nTOTAL CONDITIONS O: {len(conditions_o_triees)}\n\n")
        
        # RÉSUMÉ STATISTIQUE
        f.write("RÉSUMÉ STATISTIQUE\n")
        f.write("=" * 20 + "\n\n")
        
        if conditions_s_triees:
            meilleure_s = conditions_s_triees[0]
            f.write(f"Meilleure condition S: {meilleure_s['nom']} ({meilleure_s['pourcentage_s']:.1f}%)\n")
        
        if conditions_o_triees:
            meilleure_o = conditions_o_triees[0]
            f.write(f"Meilleure condition O: {meilleure_o['nom']} ({meilleure_o['pourcentage_o']:.1f}%)\n")
        
        # Conditions fortes
        conditions_s_fortes = [c for c in conditions_s if c['force'] == 'FORTE']
        conditions_o_fortes = [c for c in conditions_o if c['force'] == 'FORTE']
        
        f.write(f"\nConditions FORTES S (≥60%): {len(conditions_s_fortes)}\n")
        f.write(f"Conditions FORTES O (≥60%): {len(conditions_o_fortes)}\n")
        
        # Couverture
        cas_s_total = sum(c['total_cas'] for c in conditions_s)
        cas_o_total = sum(c['total_cas'] for c in conditions_o)
        
        f.write(f"\nCouverture conditions S: {cas_s_total:,} cas\n")
        f.write(f"Couverture conditions O: {cas_o_total:,} cas\n")
        f.write(f"Couverture totale: {(cas_s_total + cas_o_total) / total_donnees * 100:.1f}%\n")
    
    return nom_fichier

def afficher_resultats_principaux(conditions_s, conditions_o):
    """
    Affiche les résultats principaux
    """
    print(f"📊 RÉSULTATS PRINCIPAUX:")
    print(f"   Conditions S identifiées: {len(conditions_s)}")
    print(f"   Conditions O identifiées: {len(conditions_o)}")
    
    if conditions_s:
        meilleure_s = max(conditions_s, key=lambda x: x['pourcentage_s'])
        print(f"   Meilleure condition S: {meilleure_s['nom']} ({meilleure_s['pourcentage_s']:.1f}%)")
    
    if conditions_o:
        meilleure_o = max(conditions_o, key=lambda x: x['pourcentage_o'])
        print(f"   Meilleure condition O: {meilleure_o['nom']} ({meilleure_o['pourcentage_o']:.1f}%)")
    
    # Conditions fortes
    conditions_s_fortes = [c for c in conditions_s if c['pourcentage_s'] >= 60]
    conditions_o_fortes = [c for c in conditions_o if c['pourcentage_o'] >= 60]
    
    print(f"   Conditions FORTES S (≥60%): {len(conditions_s_fortes)}")
    print(f"   Conditions FORTES O (≥60%): {len(conditions_o_fortes)}")

if __name__ == "__main__":
    print("🚀 LANCEMENT ANALYSE COMPLÈTE CONDITIONS S/O")
    print("=" * 70)
    
    # Analyse principale
    success = analyser_conditions_predictives_so()
    
    if success:
        print(f"\n🎯 ANALYSE COMPLÈTE RÉUSSIE !")
        print("✅ Tableau prédictif S/O généré")
        print("📊 Conditions exhaustives identifiées")
        print("🚀 Système prédictif bidirectionnel créé")
    else:
        print(f"\n❌ ANALYSE COMPLÈTE ÉCHOUÉE")
        print("⚠️ Vérifiez les erreurs et corrigez les problèmes")
    
    print("\n" + "=" * 70)
