TABLEAU PRÉDICTIF EXHAUSTIF S/O AVEC DIFF
======================================================================

CORRECTION MAJEURE : INCLUSION VARIABLE DIFF
DIFF = |L4-L5| = Indicateur qualité signal prédictif

Date de génération: 2025-06-25 05:54:29
Données analysées: 561,873 points
Conditions S identifiées: 14
Conditions O identifiées: 13

SIGNIFICATION DIFF (COHÉRENCE L4/L5):
- DIFF < 0.020 : Signal PARFAIT (confiance 95%)
- DIFF < 0.030 : Signal EXCELLENT (confiance 90%)
- DIFF < 0.050 : Signal TRÈS BON (confiance 85%)
- DIFF > 0.150 : Signal DOUTEUX (abstention recommandée)

CONDITIONS QUI FAVORISENT S (CONTINUATION)
==================================================

CONDITION                          | CAS     | %S    | %O    | FORCE
----------------------------------------------------------------------
DIFF_SIGNAL_INUTILISABLE           |    557 |  80.3 |  19.7 | FORTE
DIFF_SIGNAL_TRÈS_DOUTEUX           |  3,469 |  76.4 |  23.6 | FORTE
COMB_ORDRE_FORT_DIFF_DOUTEUX       | 20,334 |  75.1 |  24.9 | FORTE
COMB_ORDRE_FORT_DIFF_EXCELLENT     |  2,964 |  73.6 |  26.4 | FORTE
L4_ORDRE_TRÈS_FORT                 | 35,378 |  71.4 |  28.6 | FORTE
L5_ORDRE_TRÈS_FORT                 | 10,198 |  69.4 |  30.6 | FORTE
DIFF_SIGNAL_DOUTEUX                | 28,975 |  65.4 |  34.6 | FORTE
COMB_VARIATIONS_FORTES_DIFF_DOUTEUX | 25,259 |  62.0 |  38.0 | FORTE
DIFF_SIGNAL_BON                    |  2,519 |  56.8 |  43.2 | MODÉRÉE
L5_ORDRE_FORT                      | 97,006 |  56.2 |  43.8 | MODÉRÉE
L4_ORDRE_FORT                      | 181,633 |  53.5 |  46.5 | FAIBLE
COMB_ORDRE_MODÉRÉ_DIFF_DOUTEUX     | 12,366 |  53.5 |  46.5 | FAIBLE
DIFF_SIGNAL_RISQUÉ                 | 182,113 |  53.1 |  46.9 | FAIBLE
COMB_ORDRE_FORT_DIFF_TRÈS_BON      | 19,867 |  52.8 |  47.2 | FAIBLE

TOTAL CONDITIONS S: 14

CONDITIONS QUI FAVORISENT O (ALTERNANCE)
==================================================

CONDITION                          | CAS     | %S    | %O    | FORCE
----------------------------------------------------------------------
L4_CHAOS_MODÉRÉ                    |    601 |  44.3 |  55.7 | MODÉRÉE
COMB_STABILITÉ_DIFF_EXCELLENT      |  7,019 |  44.6 |  55.4 | MODÉRÉE
DIFF_SIGNAL_ACCEPTABLE             | 190,784 |  45.3 |  54.7 | FAIBLE
COMB_ORDRE_MODÉRÉ_DIFF_EXCELLENT   | 77,490 |  45.4 |  54.6 | FAIBLE
L5_CHAOS_MODÉRÉ                    |  3,203 |  45.5 |  54.5 | FAIBLE
COMB_STABILITÉ_DIFF_PARFAIT        |  6,358 |  45.6 |  54.4 | FAIBLE
L4_ÉQUILIBRE                       | 25,617 |  45.7 |  54.3 | FAIBLE
L4_ORDRE_MODÉRÉ                    | 318,644 |  45.9 |  54.1 | FAIBLE
DIFF_SIGNAL_EXCELLENT              | 84,401 |  46.4 |  53.6 | FAIBLE
COMB_VARIATIONS_FORTES_DIFF_EXCELLENT | 76,975 |  46.5 |  53.5 | FAIBLE
COMB_ÉQUILIBRE_DIFF_EXCELLENT      |  3,947 |  46.7 |  53.3 | FAIBLE
L5_ÉQUILIBRE                       | 65,583 |  46.9 |  53.1 | FAIBLE
COMB_ORDRE_MODÉRÉ_DIFF_PARFAIT     |  7,594 |  47.8 |  52.2 | FAIBLE

TOTAL CONDITIONS O: 13

ANALYSE SPÉCIALE CONDITIONS DIFF
========================================

CONDITIONS DIFF FAVORISANT S:
  DIFF_SIGNAL_INUTILISABLE: 80.3% S (557 cas)
  DIFF_SIGNAL_TRÈS_DOUTEUX: 76.4% S (3,469 cas)
  COMB_ORDRE_FORT_DIFF_DOUTEUX: 75.1% S (20,334 cas)
  COMB_ORDRE_FORT_DIFF_EXCELLENT: 73.6% S (2,964 cas)
  DIFF_SIGNAL_DOUTEUX: 65.4% S (28,975 cas)
  COMB_VARIATIONS_FORTES_DIFF_DOUTEUX: 62.0% S (25,259 cas)
  DIFF_SIGNAL_BON: 56.8% S (2,519 cas)
  COMB_ORDRE_MODÉRÉ_DIFF_DOUTEUX: 53.5% S (12,366 cas)
  DIFF_SIGNAL_RISQUÉ: 53.1% S (182,113 cas)
  COMB_ORDRE_FORT_DIFF_TRÈS_BON: 52.8% S (19,867 cas)

CONDITIONS DIFF FAVORISANT O:
  COMB_STABILITÉ_DIFF_EXCELLENT: 55.4% O (7,019 cas)
  DIFF_SIGNAL_ACCEPTABLE: 54.7% O (190,784 cas)
  COMB_ORDRE_MODÉRÉ_DIFF_EXCELLENT: 54.6% O (77,490 cas)
  COMB_STABILITÉ_DIFF_PARFAIT: 54.4% O (6,358 cas)
  DIFF_SIGNAL_EXCELLENT: 53.6% O (84,401 cas)
  COMB_VARIATIONS_FORTES_DIFF_EXCELLENT: 53.5% O (76,975 cas)
  COMB_ÉQUILIBRE_DIFF_EXCELLENT: 53.3% O (3,947 cas)
  COMB_ORDRE_MODÉRÉ_DIFF_PARFAIT: 52.2% O (7,594 cas)


ANALYSES STATISTIQUES ET CORRÉLATIONS ENRICHIES
============================================================

ANALYSE AVEC ÉCARTS-TYPES
- Écarts-types : Volatilité de chaque métrique
- Analyse complète : Métriques actuelles + Volatilité

CORRÉLATIONS PRINCIPALES:
------------------------------
Diff_L4 avec DIFF     : 0.0378
Diff_L5 avec DIFF     : 0.1222
Ratio L4 avec L5      : 0.0000
Diff_L4 avec Diff_L5  : 0.0000
Ratio L4 avec DIFF    : 0.0000
Ratio L5 avec DIFF    : 0.0000

STATISTIQUES DESCRIPTIVES:
------------------------------
Diff_L4:
  Moyenne    : 0.057569
  Médiane    : 0.008995
  Écart-type : 0.071040
  Min        : 0.000000
  Max        : 0.706175
  Observations: 561,873

Diff_L5:
  Moyenne    : 0.064589
  Médiane    : 0.018673
  Écart-type : 0.111944
  Min        : 0.000000
  Max        : 0.898244
  Observations: 561,873

DIFF:
  Moyenne    : 0.084962
  Médiane    : 0.089772
  Écart-type : 0.046191
  Min        : 0.000000
  Max        : 0.680244
  Observations: 561,873

Ratio L4:
  Moyenne    : 0.505136
  Médiane    : 0.527176
  Écart-type : 0.118010
  Min        : 0.000000
  Max        : 1.097204
  Observations: 561,873

Ratio L5:
  Moyenne    : 0.579358
  Médiane    : 0.590323
  Écart-type : 0.120086
  Min        : 0.000000
  Max        : 1.134763
  Observations: 561,873

ÉCARTS-TYPES (Volatilité des métriques):
----------------------------------------
Mesure de stabilité : Plus l'écart-type est faible, plus la métrique est stable

STD_DIFF: 0.046191
STD_DIFF_L4: 0.071040
STD_DIFF_L5: 0.111944
STD_RATIO_L4: 0.118010
STD_RATIO_L5: 0.120086

TOTAL ÉCARTS-TYPES: 13 calculés

MÉTRIQUES LES PLUS STABLES (écart-type faible):
  1. std_diff: 0.046191
  2. std_diff_ratios: 0.046191
  3. std_ratio_coherence: 0.046191
  4. std_diff_l4: 0.071040
  5. std_indice_stabilite: 0.092382

MÉTRIQUES LES PLUS VOLATILES (écart-type élevé):
  1. std_somme_ratios: 0.229893
  2. std_somme_diffs: 0.144644
  3. std_produit_ratios: 0.125193
  4. std_ratio_l5: 0.120086
  5. std_ratio_l4: 0.118010

INTERPRÉTATION DES CORRÉLATIONS:
-----------------------------------
diff_l4_avec_diff_l5: FAIBLE (0.2102)
diff_l4_avec_diff: NÉGLIGEABLE (0.0378)
diff_l4_avec_ratio_l4: NÉGLIGEABLE (-0.1794)
diff_l4_avec_ratio_l5: FAIBLE (-0.2319)
diff_l4_avec_somme_ratios: FAIBLE (-0.2132)
diff_l4_avec_diff_ratios: NÉGLIGEABLE (0.0378)
diff_l4_avec_produit_ratios: NÉGLIGEABLE (-0.1806)
diff_l4_avec_moyenne_ratios: FAIBLE (-0.2132)
diff_l4_avec_somme_diffs: FORTE (0.6538)
diff_l4_avec_diff_diffs: FAIBLE (0.2940)
diff_l4_avec_ratio_coherence: NÉGLIGEABLE (-0.0378)
diff_l4_avec_indice_stabilite: NÉGLIGEABLE (-0.0093)
diff_l4_avec_std_std_diff_l4: NÉGLIGEABLE (0.0000)
diff_l4_avec_std_std_diff_l5: NÉGLIGEABLE (0.0000)
diff_l4_avec_std_std_diff: NÉGLIGEABLE (0.0000)
diff_l4_avec_std_std_ratio_l4: NÉGLIGEABLE (-0.0000)
diff_l4_avec_std_std_ratio_l5: NÉGLIGEABLE (0.0000)
diff_l4_avec_std_std_somme_ratios: NÉGLIGEABLE (0.0000)
diff_l4_avec_std_std_diff_ratios: NÉGLIGEABLE (0.0000)
diff_l4_avec_std_std_produit_ratios: NÉGLIGEABLE (0.0000)
diff_l4_avec_std_std_moyenne_ratios: NÉGLIGEABLE (0.0000)
diff_l4_avec_std_std_somme_diffs: NÉGLIGEABLE (0.0000)
diff_l4_avec_std_std_diff_diffs: NÉGLIGEABLE (0.0000)
diff_l4_avec_std_std_ratio_coherence: NÉGLIGEABLE (0.0000)
diff_l4_avec_std_std_indice_stabilite: NÉGLIGEABLE (0.0000)
diff_l5_avec_diff: NÉGLIGEABLE (0.1222)
diff_l5_avec_ratio_l4: NÉGLIGEABLE (0.1437)
diff_l5_avec_ratio_l5: NÉGLIGEABLE (0.1764)
diff_l5_avec_somme_ratios: NÉGLIGEABLE (0.1659)
diff_l5_avec_diff_ratios: NÉGLIGEABLE (0.1222)
diff_l5_avec_produit_ratios: FAIBLE (0.2317)
diff_l5_avec_moyenne_ratios: NÉGLIGEABLE (0.1659)
diff_l5_avec_somme_diffs: TRÈS FORTE (0.8772)
diff_l5_avec_diff_diffs: FORTE (0.7756)
diff_l5_avec_ratio_coherence: NÉGLIGEABLE (-0.1222)
diff_l5_avec_indice_stabilite: NÉGLIGEABLE (-0.1064)
diff_l5_avec_std_std_diff_l4: NÉGLIGEABLE (0.0000)
diff_l5_avec_std_std_diff_l5: NÉGLIGEABLE (0.0000)
diff_l5_avec_std_std_diff: NÉGLIGEABLE (0.0000)
diff_l5_avec_std_std_ratio_l4: NÉGLIGEABLE (0.0000)
diff_l5_avec_std_std_ratio_l5: NÉGLIGEABLE (0.0000)
diff_l5_avec_std_std_somme_ratios: NÉGLIGEABLE (0.0000)
diff_l5_avec_std_std_diff_ratios: NÉGLIGEABLE (0.0000)
diff_l5_avec_std_std_produit_ratios: NÉGLIGEABLE (0.0000)
diff_l5_avec_std_std_moyenne_ratios: NÉGLIGEABLE (0.0000)
diff_l5_avec_std_std_somme_diffs: NÉGLIGEABLE (0.0000)
diff_l5_avec_std_std_diff_diffs: NÉGLIGEABLE (0.0000)
diff_l5_avec_std_std_ratio_coherence: NÉGLIGEABLE (0.0000)
diff_l5_avec_std_std_indice_stabilite: NÉGLIGEABLE (0.0000)
diff_avec_ratio_l4: FAIBLE (-0.2157)
diff_avec_ratio_l5: FAIBLE (0.2825)
diff_avec_somme_ratios: NÉGLIGEABLE (0.0368)
diff_avec_diff_ratios: TRÈS FORTE (1.0000)
diff_avec_produit_ratios: NÉGLIGEABLE (0.0446)
diff_avec_moyenne_ratios: NÉGLIGEABLE (0.0368)
diff_avec_somme_diffs: NÉGLIGEABLE (0.1131)
diff_avec_diff_diffs: NÉGLIGEABLE (-0.0923)
diff_avec_ratio_coherence: TRÈS FORTE (-1.0000)
diff_avec_indice_stabilite: TRÈS FORTE (-0.9979)
diff_avec_std_std_diff_l4: NÉGLIGEABLE (0.0000)
diff_avec_std_std_diff_l5: NÉGLIGEABLE (0.0000)
diff_avec_std_std_diff: NÉGLIGEABLE (0.0000)
diff_avec_std_std_ratio_l4: NÉGLIGEABLE (-0.0000)
diff_avec_std_std_ratio_l5: NÉGLIGEABLE (0.0000)
diff_avec_std_std_somme_ratios: NÉGLIGEABLE (0.0000)
diff_avec_std_std_diff_ratios: NÉGLIGEABLE (0.0000)
diff_avec_std_std_produit_ratios: NÉGLIGEABLE (0.0000)
diff_avec_std_std_moyenne_ratios: NÉGLIGEABLE (0.0000)
diff_avec_std_std_somme_diffs: NÉGLIGEABLE (0.0000)
diff_avec_std_std_diff_diffs: NÉGLIGEABLE (0.0000)
diff_avec_std_std_ratio_coherence: NÉGLIGEABLE (0.0000)
diff_avec_std_std_indice_stabilite: NÉGLIGEABLE (0.0000)
ratio_l4_avec_ratio_l5: TRÈS FORTE (0.8646)
ratio_l4_avec_somme_ratios: TRÈS FORTE (0.9649)
ratio_l4_avec_diff_ratios: FAIBLE (-0.2157)
ratio_l4_avec_produit_ratios: TRÈS FORTE (0.9493)
ratio_l4_avec_moyenne_ratios: TRÈS FORTE (0.9649)
ratio_l4_avec_somme_diffs: NÉGLIGEABLE (0.0231)
ratio_l4_avec_diff_diffs: NÉGLIGEABLE (0.1565)
ratio_l4_avec_ratio_coherence: FAIBLE (0.2157)
ratio_l4_avec_indice_stabilite: FAIBLE (0.2004)
ratio_l4_avec_std_std_diff_l4: NÉGLIGEABLE (0.0000)
ratio_l4_avec_std_std_diff_l5: NÉGLIGEABLE (0.0000)
ratio_l4_avec_std_std_diff: NÉGLIGEABLE (0.0000)
ratio_l4_avec_std_std_ratio_l4: NÉGLIGEABLE (-0.0000)
ratio_l4_avec_std_std_ratio_l5: NÉGLIGEABLE (0.0000)
ratio_l4_avec_std_std_somme_ratios: NÉGLIGEABLE (0.0000)
ratio_l4_avec_std_std_diff_ratios: NÉGLIGEABLE (0.0000)
ratio_l4_avec_std_std_produit_ratios: NÉGLIGEABLE (0.0000)
ratio_l4_avec_std_std_moyenne_ratios: NÉGLIGEABLE (0.0000)
ratio_l4_avec_std_std_somme_diffs: NÉGLIGEABLE (0.0000)
ratio_l4_avec_std_std_diff_diffs: NÉGLIGEABLE (0.0000)
ratio_l4_avec_std_std_ratio_coherence: NÉGLIGEABLE (0.0000)
ratio_l4_avec_std_std_indice_stabilite: NÉGLIGEABLE (0.0000)
ratio_l5_avec_somme_ratios: TRÈS FORTE (0.9662)
ratio_l5_avec_diff_ratios: FAIBLE (0.2825)
ratio_l5_avec_produit_ratios: TRÈS FORTE (0.9500)
ratio_l5_avec_moyenne_ratios: TRÈS FORTE (0.9662)
ratio_l5_avec_somme_diffs: NÉGLIGEABLE (0.0226)
ratio_l5_avec_diff_diffs: NÉGLIGEABLE (0.0695)
ratio_l5_avec_ratio_coherence: FAIBLE (-0.2825)
ratio_l5_avec_indice_stabilite: FAIBLE (-0.2988)
ratio_l5_avec_std_std_diff_l4: NÉGLIGEABLE (0.0000)
ratio_l5_avec_std_std_diff_l5: NÉGLIGEABLE (0.0000)
ratio_l5_avec_std_std_diff: NÉGLIGEABLE (0.0000)
ratio_l5_avec_std_std_ratio_l4: NÉGLIGEABLE (0.0000)
ratio_l5_avec_std_std_ratio_l5: NÉGLIGEABLE (0.0000)
ratio_l5_avec_std_std_somme_ratios: NÉGLIGEABLE (0.0000)
ratio_l5_avec_std_std_diff_ratios: NÉGLIGEABLE (0.0000)
ratio_l5_avec_std_std_produit_ratios: NÉGLIGEABLE (0.0000)
ratio_l5_avec_std_std_moyenne_ratios: NÉGLIGEABLE (0.0000)
ratio_l5_avec_std_std_somme_diffs: NÉGLIGEABLE (0.0000)
ratio_l5_avec_std_std_diff_diffs: NÉGLIGEABLE (0.0000)
ratio_l5_avec_std_std_ratio_coherence: NÉGLIGEABLE (0.0000)
ratio_l5_avec_std_std_indice_stabilite: NÉGLIGEABLE (0.0000)
somme_ratios_avec_diff_ratios: NÉGLIGEABLE (0.0368)
somme_ratios_avec_produit_ratios: TRÈS FORTE (0.9835)
somme_ratios_avec_moyenne_ratios: TRÈS FORTE (1.0000)
somme_ratios_avec_somme_diffs: NÉGLIGEABLE (0.0237)
somme_ratios_avec_diff_diffs: NÉGLIGEABLE (0.1166)
somme_ratios_avec_ratio_coherence: NÉGLIGEABLE (-0.0368)
somme_ratios_avec_indice_stabilite: NÉGLIGEABLE (-0.0532)
somme_ratios_avec_std_std_diff_l4: NÉGLIGEABLE (0.0000)
somme_ratios_avec_std_std_diff_l5: NÉGLIGEABLE (0.0000)
somme_ratios_avec_std_std_diff: NÉGLIGEABLE (0.0000)
somme_ratios_avec_std_std_ratio_l4: NÉGLIGEABLE (0.0000)
somme_ratios_avec_std_std_ratio_l5: NÉGLIGEABLE (0.0000)
somme_ratios_avec_std_std_somme_ratios: NÉGLIGEABLE (0.0000)
somme_ratios_avec_std_std_diff_ratios: NÉGLIGEABLE (0.0000)
somme_ratios_avec_std_std_produit_ratios: NÉGLIGEABLE (0.0000)
somme_ratios_avec_std_std_moyenne_ratios: NÉGLIGEABLE (0.0000)
somme_ratios_avec_std_std_somme_diffs: NÉGLIGEABLE (0.0000)
somme_ratios_avec_std_std_diff_diffs: NÉGLIGEABLE (0.0000)
somme_ratios_avec_std_std_ratio_coherence: NÉGLIGEABLE (0.0000)
somme_ratios_avec_std_std_indice_stabilite: NÉGLIGEABLE (0.0000)
diff_ratios_avec_produit_ratios: NÉGLIGEABLE (0.0446)
diff_ratios_avec_moyenne_ratios: NÉGLIGEABLE (0.0368)
diff_ratios_avec_somme_diffs: NÉGLIGEABLE (0.1131)
diff_ratios_avec_diff_diffs: NÉGLIGEABLE (-0.0923)
diff_ratios_avec_ratio_coherence: TRÈS FORTE (-1.0000)
diff_ratios_avec_indice_stabilite: TRÈS FORTE (-0.9979)
diff_ratios_avec_std_std_diff_l4: NÉGLIGEABLE (0.0000)
diff_ratios_avec_std_std_diff_l5: NÉGLIGEABLE (0.0000)
diff_ratios_avec_std_std_diff: NÉGLIGEABLE (0.0000)
diff_ratios_avec_std_std_ratio_l4: NÉGLIGEABLE (-0.0000)
diff_ratios_avec_std_std_ratio_l5: NÉGLIGEABLE (0.0000)
diff_ratios_avec_std_std_somme_ratios: NÉGLIGEABLE (0.0000)
diff_ratios_avec_std_std_diff_ratios: NÉGLIGEABLE (0.0000)
diff_ratios_avec_std_std_produit_ratios: NÉGLIGEABLE (0.0000)
diff_ratios_avec_std_std_moyenne_ratios: NÉGLIGEABLE (0.0000)
diff_ratios_avec_std_std_somme_diffs: NÉGLIGEABLE (0.0000)
diff_ratios_avec_std_std_diff_diffs: NÉGLIGEABLE (0.0000)
diff_ratios_avec_std_std_ratio_coherence: NÉGLIGEABLE (0.0000)
diff_ratios_avec_std_std_indice_stabilite: NÉGLIGEABLE (0.0000)
produit_ratios_avec_moyenne_ratios: TRÈS FORTE (0.9835)
produit_ratios_avec_somme_diffs: NÉGLIGEABLE (0.0906)
produit_ratios_avec_diff_diffs: NÉGLIGEABLE (0.1723)
produit_ratios_avec_ratio_coherence: NÉGLIGEABLE (-0.0446)
produit_ratios_avec_indice_stabilite: NÉGLIGEABLE (-0.0585)
produit_ratios_avec_std_std_diff_l4: NÉGLIGEABLE (0.0000)
produit_ratios_avec_std_std_diff_l5: NÉGLIGEABLE (0.0000)
produit_ratios_avec_std_std_diff: NÉGLIGEABLE (0.0000)
produit_ratios_avec_std_std_ratio_l4: NÉGLIGEABLE (0.0000)
produit_ratios_avec_std_std_ratio_l5: NÉGLIGEABLE (0.0000)
produit_ratios_avec_std_std_somme_ratios: NÉGLIGEABLE (0.0000)
produit_ratios_avec_std_std_diff_ratios: NÉGLIGEABLE (0.0000)
produit_ratios_avec_std_std_produit_ratios: NÉGLIGEABLE (0.0000)
produit_ratios_avec_std_std_moyenne_ratios: NÉGLIGEABLE (0.0000)
produit_ratios_avec_std_std_somme_diffs: NÉGLIGEABLE (0.0000)
produit_ratios_avec_std_std_diff_diffs: NÉGLIGEABLE (0.0000)
produit_ratios_avec_std_std_ratio_coherence: NÉGLIGEABLE (0.0000)
produit_ratios_avec_std_std_indice_stabilite: NÉGLIGEABLE (0.0000)
moyenne_ratios_avec_somme_diffs: NÉGLIGEABLE (0.0237)
moyenne_ratios_avec_diff_diffs: NÉGLIGEABLE (0.1166)
moyenne_ratios_avec_ratio_coherence: NÉGLIGEABLE (-0.0368)
moyenne_ratios_avec_indice_stabilite: NÉGLIGEABLE (-0.0532)
moyenne_ratios_avec_std_std_diff_l4: NÉGLIGEABLE (0.0000)
moyenne_ratios_avec_std_std_diff_l5: NÉGLIGEABLE (0.0000)
moyenne_ratios_avec_std_std_diff: NÉGLIGEABLE (0.0000)
moyenne_ratios_avec_std_std_ratio_l4: NÉGLIGEABLE (0.0000)
moyenne_ratios_avec_std_std_ratio_l5: NÉGLIGEABLE (0.0000)
moyenne_ratios_avec_std_std_somme_ratios: NÉGLIGEABLE (0.0000)
moyenne_ratios_avec_std_std_diff_ratios: NÉGLIGEABLE (0.0000)
moyenne_ratios_avec_std_std_produit_ratios: NÉGLIGEABLE (0.0000)
moyenne_ratios_avec_std_std_moyenne_ratios: NÉGLIGEABLE (0.0000)
moyenne_ratios_avec_std_std_somme_diffs: NÉGLIGEABLE (0.0000)
moyenne_ratios_avec_std_std_diff_diffs: NÉGLIGEABLE (0.0000)
moyenne_ratios_avec_std_std_ratio_coherence: NÉGLIGEABLE (0.0000)
moyenne_ratios_avec_std_std_indice_stabilite: NÉGLIGEABLE (0.0000)
somme_diffs_avec_diff_diffs: FORTE (0.7447)
somme_diffs_avec_ratio_coherence: NÉGLIGEABLE (-0.1131)
somme_diffs_avec_indice_stabilite: NÉGLIGEABLE (-0.0869)
somme_diffs_avec_std_std_diff_l4: NÉGLIGEABLE (0.0000)
somme_diffs_avec_std_std_diff_l5: NÉGLIGEABLE (0.0000)
somme_diffs_avec_std_std_diff: NÉGLIGEABLE (0.0000)
somme_diffs_avec_std_std_ratio_l4: NÉGLIGEABLE (0.0000)
somme_diffs_avec_std_std_ratio_l5: NÉGLIGEABLE (0.0000)
somme_diffs_avec_std_std_somme_ratios: NÉGLIGEABLE (0.0000)
somme_diffs_avec_std_std_diff_ratios: NÉGLIGEABLE (0.0000)
somme_diffs_avec_std_std_produit_ratios: NÉGLIGEABLE (0.0000)
somme_diffs_avec_std_std_moyenne_ratios: NÉGLIGEABLE (0.0000)
somme_diffs_avec_std_std_somme_diffs: NÉGLIGEABLE (0.0000)
somme_diffs_avec_std_std_diff_diffs: NÉGLIGEABLE (0.0000)
somme_diffs_avec_std_std_ratio_coherence: NÉGLIGEABLE (0.0000)
somme_diffs_avec_std_std_indice_stabilite: NÉGLIGEABLE (0.0000)
diff_diffs_avec_ratio_coherence: NÉGLIGEABLE (0.0923)
diff_diffs_avec_indice_stabilite: NÉGLIGEABLE (0.1091)
diff_diffs_avec_std_std_diff_l4: NÉGLIGEABLE (0.0000)
diff_diffs_avec_std_std_diff_l5: NÉGLIGEABLE (0.0000)
diff_diffs_avec_std_std_diff: NÉGLIGEABLE (0.0000)
diff_diffs_avec_std_std_ratio_l4: NÉGLIGEABLE (0.0000)
diff_diffs_avec_std_std_ratio_l5: NÉGLIGEABLE (0.0000)
diff_diffs_avec_std_std_somme_ratios: NÉGLIGEABLE (0.0000)
diff_diffs_avec_std_std_diff_ratios: NÉGLIGEABLE (0.0000)
diff_diffs_avec_std_std_produit_ratios: NÉGLIGEABLE (0.0000)
diff_diffs_avec_std_std_moyenne_ratios: NÉGLIGEABLE (0.0000)
diff_diffs_avec_std_std_somme_diffs: NÉGLIGEABLE (0.0000)
diff_diffs_avec_std_std_diff_diffs: NÉGLIGEABLE (0.0000)
diff_diffs_avec_std_std_ratio_coherence: NÉGLIGEABLE (0.0000)
diff_diffs_avec_std_std_indice_stabilite: NÉGLIGEABLE (0.0000)
ratio_coherence_avec_indice_stabilite: TRÈS FORTE (0.9979)
ratio_coherence_avec_std_std_diff_l4: NÉGLIGEABLE (0.0000)
ratio_coherence_avec_std_std_diff_l5: NÉGLIGEABLE (0.0000)
ratio_coherence_avec_std_std_diff: NÉGLIGEABLE (0.0000)
ratio_coherence_avec_std_std_ratio_l4: NÉGLIGEABLE (-0.0000)
ratio_coherence_avec_std_std_ratio_l5: NÉGLIGEABLE (0.0000)
ratio_coherence_avec_std_std_somme_ratios: NÉGLIGEABLE (0.0000)
ratio_coherence_avec_std_std_diff_ratios: NÉGLIGEABLE (0.0000)
ratio_coherence_avec_std_std_produit_ratios: NÉGLIGEABLE (0.0000)
ratio_coherence_avec_std_std_moyenne_ratios: NÉGLIGEABLE (0.0000)
ratio_coherence_avec_std_std_somme_diffs: NÉGLIGEABLE (0.0000)
ratio_coherence_avec_std_std_diff_diffs: NÉGLIGEABLE (0.0000)
ratio_coherence_avec_std_std_ratio_coherence: NÉGLIGEABLE (0.0000)
ratio_coherence_avec_std_std_indice_stabilite: NÉGLIGEABLE (0.0000)
indice_stabilite_avec_std_std_diff_l4: NÉGLIGEABLE (0.0000)
indice_stabilite_avec_std_std_diff_l5: NÉGLIGEABLE (0.0000)
indice_stabilite_avec_std_std_diff: NÉGLIGEABLE (0.0000)
indice_stabilite_avec_std_std_ratio_l4: NÉGLIGEABLE (-0.0000)
indice_stabilite_avec_std_std_ratio_l5: NÉGLIGEABLE (0.0000)
indice_stabilite_avec_std_std_somme_ratios: NÉGLIGEABLE (0.0000)
indice_stabilite_avec_std_std_diff_ratios: NÉGLIGEABLE (0.0000)
indice_stabilite_avec_std_std_produit_ratios: NÉGLIGEABLE (0.0000)
indice_stabilite_avec_std_std_moyenne_ratios: NÉGLIGEABLE (0.0000)
indice_stabilite_avec_std_std_somme_diffs: NÉGLIGEABLE (0.0000)
indice_stabilite_avec_std_std_diff_diffs: NÉGLIGEABLE (0.0000)
indice_stabilite_avec_std_std_ratio_coherence: NÉGLIGEABLE (0.0000)
indice_stabilite_avec_std_std_indice_stabilite: NÉGLIGEABLE (0.0000)
std_std_diff_l4_avec_std_std_diff_l5: NÉGLIGEABLE (0.0000)
std_std_diff_l4_avec_std_std_diff: NÉGLIGEABLE (0.0000)
std_std_diff_l4_avec_std_std_ratio_l4: NÉGLIGEABLE (0.0000)
std_std_diff_l4_avec_std_std_ratio_l5: NÉGLIGEABLE (0.0000)
std_std_diff_l4_avec_std_std_somme_ratios: NÉGLIGEABLE (0.0000)
std_std_diff_l4_avec_std_std_diff_ratios: NÉGLIGEABLE (0.0000)
std_std_diff_l4_avec_std_std_produit_ratios: NÉGLIGEABLE (0.0000)
std_std_diff_l4_avec_std_std_moyenne_ratios: NÉGLIGEABLE (0.0000)
std_std_diff_l4_avec_std_std_somme_diffs: NÉGLIGEABLE (0.0000)
std_std_diff_l4_avec_std_std_diff_diffs: NÉGLIGEABLE (0.0000)
std_std_diff_l4_avec_std_std_ratio_coherence: NÉGLIGEABLE (0.0000)
std_std_diff_l4_avec_std_std_indice_stabilite: NÉGLIGEABLE (0.0000)
std_std_diff_l5_avec_std_std_diff: NÉGLIGEABLE (0.0000)
std_std_diff_l5_avec_std_std_ratio_l4: NÉGLIGEABLE (0.0000)
std_std_diff_l5_avec_std_std_ratio_l5: NÉGLIGEABLE (0.0000)
std_std_diff_l5_avec_std_std_somme_ratios: NÉGLIGEABLE (0.0000)
std_std_diff_l5_avec_std_std_diff_ratios: NÉGLIGEABLE (0.0000)
std_std_diff_l5_avec_std_std_produit_ratios: NÉGLIGEABLE (0.0000)
std_std_diff_l5_avec_std_std_moyenne_ratios: NÉGLIGEABLE (0.0000)
std_std_diff_l5_avec_std_std_somme_diffs: NÉGLIGEABLE (0.0000)
std_std_diff_l5_avec_std_std_diff_diffs: NÉGLIGEABLE (0.0000)
std_std_diff_l5_avec_std_std_ratio_coherence: NÉGLIGEABLE (0.0000)
std_std_diff_l5_avec_std_std_indice_stabilite: NÉGLIGEABLE (0.0000)
std_std_diff_avec_std_std_ratio_l4: NÉGLIGEABLE (0.0000)
std_std_diff_avec_std_std_ratio_l5: NÉGLIGEABLE (0.0000)
std_std_diff_avec_std_std_somme_ratios: NÉGLIGEABLE (0.0000)
std_std_diff_avec_std_std_diff_ratios: NÉGLIGEABLE (0.0000)
std_std_diff_avec_std_std_produit_ratios: NÉGLIGEABLE (0.0000)
std_std_diff_avec_std_std_moyenne_ratios: NÉGLIGEABLE (0.0000)
std_std_diff_avec_std_std_somme_diffs: NÉGLIGEABLE (0.0000)
std_std_diff_avec_std_std_diff_diffs: NÉGLIGEABLE (0.0000)
std_std_diff_avec_std_std_ratio_coherence: NÉGLIGEABLE (0.0000)
std_std_diff_avec_std_std_indice_stabilite: NÉGLIGEABLE (0.0000)
std_std_ratio_l4_avec_std_std_ratio_l5: NÉGLIGEABLE (0.0000)
std_std_ratio_l4_avec_std_std_somme_ratios: NÉGLIGEABLE (0.0000)
std_std_ratio_l4_avec_std_std_diff_ratios: NÉGLIGEABLE (0.0000)
std_std_ratio_l4_avec_std_std_produit_ratios: NÉGLIGEABLE (0.0000)
std_std_ratio_l4_avec_std_std_moyenne_ratios: NÉGLIGEABLE (0.0000)
std_std_ratio_l4_avec_std_std_somme_diffs: NÉGLIGEABLE (0.0000)
std_std_ratio_l4_avec_std_std_diff_diffs: NÉGLIGEABLE (0.0000)
std_std_ratio_l4_avec_std_std_ratio_coherence: NÉGLIGEABLE (0.0000)
std_std_ratio_l4_avec_std_std_indice_stabilite: NÉGLIGEABLE (0.0000)
std_std_ratio_l5_avec_std_std_somme_ratios: NÉGLIGEABLE (0.0000)
std_std_ratio_l5_avec_std_std_diff_ratios: NÉGLIGEABLE (0.0000)
std_std_ratio_l5_avec_std_std_produit_ratios: NÉGLIGEABLE (0.0000)
std_std_ratio_l5_avec_std_std_moyenne_ratios: NÉGLIGEABLE (0.0000)
std_std_ratio_l5_avec_std_std_somme_diffs: NÉGLIGEABLE (0.0000)
std_std_ratio_l5_avec_std_std_diff_diffs: NÉGLIGEABLE (0.0000)
std_std_ratio_l5_avec_std_std_ratio_coherence: NÉGLIGEABLE (0.0000)
std_std_ratio_l5_avec_std_std_indice_stabilite: NÉGLIGEABLE (0.0000)
std_std_somme_ratios_avec_std_std_diff_ratios: NÉGLIGEABLE (0.0000)
std_std_somme_ratios_avec_std_std_produit_ratios: NÉGLIGEABLE (0.0000)
std_std_somme_ratios_avec_std_std_moyenne_ratios: NÉGLIGEABLE (0.0000)
std_std_somme_ratios_avec_std_std_somme_diffs: NÉGLIGEABLE (0.0000)
std_std_somme_ratios_avec_std_std_diff_diffs: NÉGLIGEABLE (0.0000)
std_std_somme_ratios_avec_std_std_ratio_coherence: NÉGLIGEABLE (0.0000)
std_std_somme_ratios_avec_std_std_indice_stabilite: NÉGLIGEABLE (0.0000)
std_std_diff_ratios_avec_std_std_produit_ratios: NÉGLIGEABLE (0.0000)
std_std_diff_ratios_avec_std_std_moyenne_ratios: NÉGLIGEABLE (0.0000)
std_std_diff_ratios_avec_std_std_somme_diffs: NÉGLIGEABLE (0.0000)
std_std_diff_ratios_avec_std_std_diff_diffs: NÉGLIGEABLE (0.0000)
std_std_diff_ratios_avec_std_std_ratio_coherence: NÉGLIGEABLE (0.0000)
std_std_diff_ratios_avec_std_std_indice_stabilite: NÉGLIGEABLE (0.0000)
std_std_produit_ratios_avec_std_std_moyenne_ratios: NÉGLIGEABLE (0.0000)
std_std_produit_ratios_avec_std_std_somme_diffs: NÉGLIGEABLE (0.0000)
std_std_produit_ratios_avec_std_std_diff_diffs: NÉGLIGEABLE (0.0000)
std_std_produit_ratios_avec_std_std_ratio_coherence: NÉGLIGEABLE (0.0000)
std_std_produit_ratios_avec_std_std_indice_stabilite: NÉGLIGEABLE (0.0000)
std_std_moyenne_ratios_avec_std_std_somme_diffs: NÉGLIGEABLE (0.0000)
std_std_moyenne_ratios_avec_std_std_diff_diffs: NÉGLIGEABLE (0.0000)
std_std_moyenne_ratios_avec_std_std_ratio_coherence: NÉGLIGEABLE (0.0000)
std_std_moyenne_ratios_avec_std_std_indice_stabilite: NÉGLIGEABLE (0.0000)
std_std_somme_diffs_avec_std_std_diff_diffs: NÉGLIGEABLE (0.0000)
std_std_somme_diffs_avec_std_std_ratio_coherence: NÉGLIGEABLE (0.0000)
std_std_somme_diffs_avec_std_std_indice_stabilite: NÉGLIGEABLE (0.0000)
std_std_diff_diffs_avec_std_std_ratio_coherence: NÉGLIGEABLE (0.0000)
std_std_diff_diffs_avec_std_std_indice_stabilite: NÉGLIGEABLE (0.0000)
std_std_ratio_coherence_avec_std_std_indice_stabilite: NÉGLIGEABLE (0.0000)


FORMULES MATHÉMATIQUES OPÉRATIONNELLES
==================================================

SEUILS DE DÉCISION OPTIMAUX:
------------------------------
DIFF (Cohérence L4/L5):
  PARFAIT: DIFF <= 0.007112
  EXCELLENT: DIFF <= 0.020672
  TRES_BON: DIFF <= 0.024475
  ACCEPTABLE: DIFF <= 0.089772
  DOUTEUX: DIFF <= 0.117510
  INUTILISABLE: DIFF <= 0.153428

RATIO L4 (Entropie locale/globale):
  CHAOS: ratio_l4 >= 0.389003
  EQUILIBRE: ratio_l4 >= 0.527176
  ORDRE_MODERE: ratio_l4 >= 0.555933
  ORDRE_FORT: ratio_l4 >= 0.600211
  ORDRE_TRES_FORT: ratio_l4 >= 0.686347

RATIO L5 (Entropie locale/globale):
  CHAOS: ratio_l5 >= 0.415613
  EQUILIBRE: ratio_l5 >= 0.590323
  ORDRE_MODERE: ratio_l5 >= 0.633838
  ORDRE_FORT: ratio_l5 >= 0.683649
  ORDRE_TRES_FORT: ratio_l5 >= 0.787038

FORMULES DE CALCUL APPLICABLES:
-----------------------------------
SCORE_CONTINUATION:
  Formule: SCORE_S = (1 - DIFF/0.3) * 0.4 + (ratio_l4 - 0.5) * 0.3 + (ratio_l5 - 0.5) * 0.3
  Usage: Plus le score est élevé, plus S est probable
  Seuil: >= 0.6

SCORE_ALTERNANCE:
  Formule: SCORE_O = DIFF * 0.5 + (0.5 - abs(ratio_l4 - 0.5)) * 0.25 + (0.5 - abs(ratio_l5 - 0.5)) * 0.25
  Usage: Plus le score est élevé, plus O est probable
  Seuil: >= 0.4

INDICE_COHERENCE:
  Formule: COHERENCE = 1 - DIFF - abs(ratio_l4 - ratio_l5)
  Usage: Mesure la cohérence globale du signal
  Fiabilité: >= 0.7

STRATÉGIES PAR POSITION DE MAIN:
-----------------------------------
MAINS_5_10:
  Condition: main >= 5 and main <= 10
  Stratégie: Privilégier DIFF < 0.05 pour prédictions fiables
  Formule: if DIFF < 0.05: prediction = "S" if ratio_l4 > 0.6 else "O"

MAINS_11_50:
  Condition: main >= 11 and main <= 50
  Stratégie: Utiliser combinaison DIFF + ratios
  Formule: prediction = "S" if (DIFF > 0.15 and ratio_l4 > 0.7) else "O"

MAINS_50_PLUS:
  Condition: main > 50
  Stratégie: Privilégier stabilité des ratios
  Formule: prediction = "S" if abs(ratio_l4 - ratio_l5) < 0.1 else "O"



TABLEAU EXHAUSTIF DES CORRÉLATIONS ET IMPACT S/O
======================================================================

CORRÉLATIONS TRIÉES PAR FORCE PRÉDICTIVE:
--------------------------------------------------
CORRÉLATION                           | GLOBALE | S      | O      | DIFF   | FAVORISE | FORCE
----------------------------------------------------------------------------------------------------
diff_l4→std_std_diff_l4               |  0.000 |    nan |  0.000 |    nan |        O | FAIBLE
diff_l4→std_std_diff_l5               |  0.000 |  0.000 |    nan |    nan |        O | FAIBLE
diff_l4→std_std_ratio_l5              |  0.000 |    nan |  0.000 |    nan |        O | FAIBLE
diff_l4→std_std_produit_ratios        | -0.000 |    nan |    nan |    nan |        O | FAIBLE
diff_l4→std_std_somme_diffs           |  0.000 |  0.000 |    nan |    nan |        O | FAIBLE
diff_l4→std_std_indice_stabilite      |    nan |    nan |    nan |    nan |        O | FAIBLE
somme_ratios→ratio_coherence          | -0.037 |  0.030 | -0.139 |  0.170 |        O | MODÉRÉE
ratio_l4→indice_stabilite             |  0.200 |  0.254 |  0.119 |  0.136 |        S | MODÉRÉE
diff_l5→ratio_l4                      |  0.144 |  0.124 |  0.170 |  0.047 |        O | FAIBLE
diff_l5→somme_ratios                  |  0.166 |  0.149 |  0.188 |  0.039 |        O | FAIBLE
diff_l5→moyenne_ratios                |  0.166 |  0.149 |  0.188 |  0.039 |        O | FAIBLE
diff_l4→somme_diffs                   |  0.654 |  0.668 |  0.638 |  0.030 |        S | FAIBLE
diff_l4→ratio_l5                      | -0.232 | -0.247 | -0.217 |  0.030 |        S | FAIBLE
diff_l5→produit_ratios                |  0.232 |  0.219 |  0.247 |  0.028 |        O | FAIBLE
diff_l4→diff_diffs                    |  0.294 |  0.307 |  0.280 |  0.027 |        S | FAIBLE
diff_l5→ratio_l5                      |  0.176 |  0.164 |  0.191 |  0.027 |        O | FAIBLE
diff_l4→diff_l5                       |  0.210 |  0.222 |  0.198 |  0.024 |        S | FAIBLE
diff_l4→somme_ratios                  | -0.213 | -0.225 | -0.202 |  0.023 |        S | FAIBLE
diff_l4→moyenne_ratios                | -0.213 | -0.225 | -0.202 |  0.023 |        S | FAIBLE
diff_l4→ratio_l4                      | -0.179 | -0.189 | -0.171 |  0.017 |        S | FAIBLE
diff_l5→ratio_coherence               | -0.122 | -0.129 | -0.116 |  0.014 |        S | FAIBLE
diff_l5→diff                          |  0.122 |  0.129 |  0.116 |  0.014 |        S | FAIBLE
diff_l5→diff_ratios                   |  0.122 |  0.129 |  0.116 |  0.014 |        S | FAIBLE
diff_l4→produit_ratios                | -0.181 | -0.188 | -0.175 |  0.013 |        S | FAIBLE
diff_l5→indice_stabilite              | -0.106 | -0.112 | -0.101 |  0.010 |        S | FAIBLE
diff_l4→indice_stabilite              | -0.009 | -0.006 | -0.013 |  0.007 |        O | FAIBLE
diff_l4→ratio_coherence               | -0.038 | -0.038 | -0.038 |  0.001 |        O | FAIBLE
diff_l4→diff                          |  0.038 |  0.038 |  0.038 |  0.001 |        O | FAIBLE
diff_l4→diff_ratios                   |  0.038 |  0.038 |  0.038 |  0.001 |        O | FAIBLE
diff_l4→std_std_ratio_coherence       |  0.000 |  0.000 | -0.000 |  0.000 |        O | FAIBLE

LÉGENDE:
- GLOBALE: Corrélation sur toutes les données
- S: Corrélation spécifique aux cas de continuation
- O: Corrélation spécifique aux cas d'alternance
- DIFF: |Corr_S - Corr_O| = Force discriminante
- FAVORISE: S ou O selon la corrélation la plus forte
- FORCE: FORTE (>0.2), MODÉRÉE (>0.1), FAIBLE (≤0.1)

MÉTRIQUES ANALYSÉES:
--------------------
 1. diff_l4
 2. diff_l5
 3. diff
 4. ratio_l4
 5. ratio_l5
 6. somme_ratios
 7. diff_ratios
 8. produit_ratios
 9. moyenne_ratios
10. somme_diffs
11. diff_diffs
12. ratio_coherence
13. indice_stabilite
14. std_std_diff_l4
15. std_std_diff_l5
16. std_std_diff
17. std_std_ratio_l4
18. std_std_ratio_l5
19. std_std_somme_ratios
20. std_std_diff_ratios
21. std_std_produit_ratios
22. std_std_moyenne_ratios
23. std_std_somme_diffs
24. std_std_diff_diffs
25. std_std_ratio_coherence
26. std_std_indice_stabilite

TOTAL: 26 métriques × 25/2 = 325 corrélations calculées
