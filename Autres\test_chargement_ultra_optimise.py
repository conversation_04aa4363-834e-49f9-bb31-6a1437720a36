#!/usr/bin/env python3
"""
TEST DU CHARGEMENT ULTRA-OPTIMISÉ
=================================

Test pour vérifier que les vraies optimisations de chargement
sont maintenant intégrées dans AnalyseurEntropiqueIntegre.
"""

import time
import os
from datetime import datetime


def test_chargement_ultra_optimise():
    """Test du nouveau système de chargement ultra-optimisé"""
    
    print("🚀 TEST DU CHARGEMENT ULTRA-OPTIMISÉ")
    print("=" * 50)
    
    dataset_path = "dataset_baccarat_lupasco_20250622_011427.json"
    
    if not os.path.exists(dataset_path):
        print(f"❌ Dataset non trouvé : {dataset_path}")
        return False
    
    # Taille du fichier
    size_gb = os.path.getsize(dataset_path) / (1024**3)
    print(f"📊 Taille dataset : {size_gb:.2f} GB")
    
    try:
        from analyseur_transitions_index5 import AnalyseurEntropiqueIntegre
        
        print(f"\n🎯 Test avec le nouveau système ultra-optimisé...")
        start_time = time.time()
        
        # Initialisation avec optimisations
        analyseur = AnalyseurEntropiqueIntegre(dataset_path)
        
        temps_init = time.time() - start_time
        print(f"✅ Initialisation : {temps_init:.2f}s")
        
        # Test de chargement optimisé avec 10 parties
        print(f"\n💾 Test chargement optimisé (10 parties)...")
        start_chargement = time.time()
        
        parties = analyseur._charger_dataset_optimise(nb_parties_max=10)
        
        temps_chargement = time.time() - start_chargement
        
        if isinstance(parties, list):
            print(f"✅ Chargement réussi en {temps_chargement:.3f}s")
            print(f"📊 Parties chargées : {len(parties)}")
            
            # Vérifier la structure
            if parties and len(parties) > 0:
                premiere_partie = parties[0]
                print(f"🔍 Vérification structure :")
                print(f"   Partie ID : {premiere_partie.get('partie_number', 'N/A')}")
                print(f"   Nombre de mains : {len(premiere_partie.get('mains', []))}")
                
                if premiere_partie.get('mains'):
                    premiere_main = premiere_partie['mains'][0]
                    print(f"   Première main : {premiere_main.get('index5_combined', 'N/A')}")
            
            # Test du cache (deuxième chargement)
            print(f"\n🚀 Test du cache (deuxième chargement)...")
            start_cache = time.time()
            
            parties_cache = analyseur._charger_dataset_optimise(nb_parties_max=10)
            
            temps_cache = time.time() - start_cache
            
            print(f"✅ Chargement depuis cache : {temps_cache:.3f}s")
            print(f"📊 Parties depuis cache : {len(parties_cache)}")
            
            # Calculer l'accélération
            if temps_cache > 0:
                acceleration = temps_chargement / temps_cache
                print(f"🚀 Accélération cache : {acceleration:.1f}x")
            
            return True
        else:
            print(f"❌ Erreur chargement : {parties}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur test : {e}")
        import traceback
        traceback.print_exc()
        return False


def test_analyse_avec_optimisations():
    """Test d'analyse complète avec les nouvelles optimisations"""
    
    print(f"\n🧪 TEST ANALYSE AVEC OPTIMISATIONS")
    print("=" * 40)
    
    dataset_path = "dataset_baccarat_lupasco_20250622_011427.json"
    
    try:
        from analyseur_transitions_index5 import AnalyseurEntropiqueIntegre
        
        print("🎯 Test analyse optimisée (3 parties)...")
        start_time = time.time()
        
        analyseur = AnalyseurEntropiqueIntegre(dataset_path)
        resultats = analyseur.analyser_dataset_complet(nb_parties_max=3)
        
        temps_total = time.time() - start_time
        
        if 'erreur' not in resultats:
            print(f"✅ Analyse réussie en {temps_total:.2f}s")
            
            stats = resultats['statistiques_globales']
            print(f"📊 Parties analysées : {resultats['nb_parties_analysees']}")
            print(f"🎯 Prédictions : {stats['total_predictions_globales']}")
            print(f"✅ Taux succès : {stats['taux_succes_global']:.2f}%")
            
            # Calculer la performance par partie
            temps_par_partie = temps_total / resultats['nb_parties_analysees']
            print(f"⚡ Temps par partie : {temps_par_partie:.2f}s")
            
            # Comparer avec la performance précédente
            temps_precedent = 18.92  # Résultat du test précédent
            if temps_par_partie < temps_precedent:
                amelioration = temps_precedent / temps_par_partie
                print(f"🚀 Amélioration : {amelioration:.1f}x plus rapide !")
            else:
                print(f"⚠️ Performance similaire : {temps_par_partie:.2f}s vs {temps_precedent:.2f}s")
            
            # Extrapolation pour 100,000 parties
            temps_100k = temps_par_partie * 100000 / 3600  # en heures
            print(f"📈 Estimation 100,000 parties : {temps_100k:.1f}h")
            
            return temps_par_partie
        else:
            print(f"❌ Erreur analyse : {resultats['erreur']}")
            return None
            
    except Exception as e:
        print(f"❌ Erreur test analyse : {e}")
        import traceback
        traceback.print_exc()
        return None


def verifier_optimisations_actives():
    """Vérifie quelles optimisations sont actives"""
    
    print(f"\n🔍 VÉRIFICATION DES OPTIMISATIONS ACTIVES")
    print("=" * 45)
    
    optimisations = {}
    
    # Numba
    try:
        from analyseur_transitions_index5 import HAS_NUMBA
        optimisations['numba'] = HAS_NUMBA
        print(f"{'✅' if HAS_NUMBA else '❌'} Numba JIT : {'Activé' if HAS_NUMBA else 'Désactivé'}")
    except:
        optimisations['numba'] = False
        print("❌ Numba JIT : Non disponible")
    
    # orjson
    try:
        from analyseur_transitions_index5 import HAS_ORJSON
        optimisations['orjson'] = HAS_ORJSON
        print(f"{'✅' if HAS_ORJSON else '❌'} orjson : {'Activé' if HAS_ORJSON else 'Désactivé'}")
    except:
        optimisations['orjson'] = False
        print("❌ orjson : Non disponible")
    
    # ijson
    try:
        from analyseur_transitions_index5 import HAS_IJSON
        optimisations['ijson'] = HAS_IJSON
        print(f"{'✅' if HAS_IJSON else '❌'} ijson : {'Activé' if HAS_IJSON else 'Désactivé'}")
    except:
        optimisations['ijson'] = False
        print("❌ ijson : Non disponible")
    
    # Multiprocessing
    import multiprocessing
    nb_cores = multiprocessing.cpu_count()
    optimisations['cores'] = nb_cores
    print(f"✅ Multiprocessing : {nb_cores} cœurs")
    
    # RAM
    try:
        import psutil
        ram_gb = psutil.virtual_memory().total / (1024**3)
        optimisations['ram_gb'] = ram_gb
        print(f"✅ RAM : {ram_gb:.1f} GB")
    except:
        optimisations['ram_gb'] = 'Inconnu'
        print("⚠️ RAM : Impossible à déterminer")
    
    return optimisations


def estimer_performance_finale():
    """Estime la performance finale avec toutes les optimisations"""
    
    print(f"\n📈 ESTIMATION PERFORMANCE FINALE")
    print("=" * 35)
    
    # Performance actuelle observée
    temps_actuel = 18.92  # secondes par partie
    
    # Gains estimés par optimisation
    gains = {
        'Cache intelligent': 5,    # 3-10x observé
        'Memory mapping': 2,       # 1.5-3x
        'Chunks optimisés': 3,     # 2-5x
        'Numba JIT': 10,          # 5-20x pour calculs entropiques
    }
    
    print(f"📊 Performance actuelle : {temps_actuel:.2f}s/partie")
    print(f"\n🚀 Gains estimés :")
    
    gain_total = 1
    for opt, gain in gains.items():
        gain_total *= gain
        print(f"   {opt:20s} : {gain:2d}x")
    
    print(f"\n⚡ GAIN TOTAL ESTIMÉ : {gain_total:,}x")
    
    # Performance cible
    temps_optimise = temps_actuel / gain_total
    temps_100k_minutes = temps_optimise * 100000 / 60
    
    print(f"\n🎯 PERFORMANCE CIBLE :")
    print(f"   Par partie : {temps_optimise:.4f}s")
    print(f"   100,000 parties : {temps_100k_minutes:.1f} minutes")
    
    if temps_100k_minutes < 60:
        print(f"   🔥 OBJECTIF ATTEINT : Moins d'1 heure !")
    
    return gain_total


if __name__ == "__main__":
    print("🚀 TEST DU SYSTÈME ULTRA-OPTIMISÉ")
    print("=" * 60)
    
    # 1. Vérifier les optimisations disponibles
    optimisations = verifier_optimisations_actives()
    
    # 2. Test du chargement ultra-optimisé
    chargement_ok = test_chargement_ultra_optimise()
    
    # 3. Test d'analyse avec optimisations
    temps_par_partie = test_analyse_avec_optimisations()
    
    # 4. Estimation performance finale
    gain_theorique = estimer_performance_finale()
    
    # Résumé final
    print(f"\n🎉 RÉSUMÉ DES TESTS")
    print("=" * 25)
    
    if chargement_ok:
        print(f"✅ Chargement ultra-optimisé : Fonctionnel")
    else:
        print(f"❌ Chargement ultra-optimisé : Problème")
    
    if temps_par_partie:
        print(f"✅ Analyse optimisée : {temps_par_partie:.2f}s/partie")
        
        # Comparaison avec performance initiale
        temps_initial = 34.26  # Performance initiale
        if temps_par_partie < temps_initial:
            amelioration_totale = temps_initial / temps_par_partie
            print(f"🚀 Amélioration totale : {amelioration_totale:.1f}x")
        
        # Estimation finale
        if optimisations.get('numba', False):
            print(f"⚡ Avec Numba : Performance encore 10x plus rapide possible")
    else:
        print(f"❌ Analyse optimisée : Échec")
    
    print(f"\n🎯 PROCHAINE ÉTAPE : Lancer l'analyse complète ultra-optimisée !")
