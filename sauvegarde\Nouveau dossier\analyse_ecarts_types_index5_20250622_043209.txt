================================================================================
ANALYSE SPÉCIALISÉE DES ÉCARTS-TYPES INDEX5
Expertise: Statisticien PhD - Analyse de variabilité
Généré le : 2025-06-22 04:32:09
================================================================================

📊 RÉSUMÉ EXÉCUTIF
========================================
Valeurs INDEX5 analysées : 18
Anomalies détectées : 0

SYNCHRONISATION
===============
A_BANKER: {'diff_absolue': 0.*****************, 'ratio': 1.****************, 'sigma_0': 16.***************, 'sigma_1': 16.***************, 'stabilite': 'STABLE', 'plus_stable': '1_DESYNC', 'effet_sync': 'NEGLIGEABLE'}
A_PLAYER: {'diff_absolue': 0.*****************, 'ratio': 1.****************, 'sigma_0': 16.***************, 'sigma_1': 16.***************, 'stabilite': 'STABLE', 'plus_stable': '1_DESYNC', 'effet_sync': 'NEGLIGEABLE'}
A_TIE: {'diff_absolue': 0.062016246377528006, 'ratio': 0.****************, 'sigma_0': 19.**************, 'sigma_1': 19.***************, 'stabilite': 'STABLE', 'plus_stable': '0_SYNC', 'effet_sync': 'NEGLIGEABLE'}
B_BANKER: {'diff_absolue': 0.****************, 'ratio': 1.****************, 'sigma_0': 18.**************, 'sigma_1': 18.***************, 'stabilite': 'STABLE', 'plus_stable': '1_DESYNC', 'effet_sync': 'NEGLIGEABLE'}
B_PLAYER: {'diff_absolue': 0.0054345819741712376, 'ratio': 0.****************, 'sigma_0': 17.***************, 'sigma_1': 17.**************, 'stabilite': 'STABLE', 'plus_stable': '0_SYNC', 'effet_sync': 'NEGLIGEABLE'}
B_TIE: {'diff_absolue': 0.016514322411548932, 'ratio': 0.****************, 'sigma_0': 19.***************, 'sigma_1': 19.***************, 'stabilite': 'STABLE', 'plus_stable': '0_SYNC', 'effet_sync': 'NEGLIGEABLE'}
C_BANKER: {'diff_absolue': 0.*****************, 'ratio': 1.****************, 'sigma_0': 16.**************, 'sigma_1': 15.***************, 'stabilite': 'STABLE', 'plus_stable': '1_DESYNC', 'effet_sync': 'NEGLIGEABLE'}
C_PLAYER: {'diff_absolue': 0.****************, 'ratio': 1.****************, 'sigma_0': 18.***************, 'sigma_1': 18.***************, 'stabilite': 'STABLE', 'plus_stable': '1_DESYNC', 'effet_sync': 'NEGLIGEABLE'}
C_TIE: {'diff_absolue': 0.*****************, 'ratio': 1.****************, 'sigma_0': 18.**************, 'sigma_1': 18.***************, 'stabilite': 'STABLE', 'plus_stable': '1_DESYNC', 'effet_sync': 'NEGLIGEABLE'}

INDEX2 PATTERNS
===============
0_BANKER: {'hierarchie': [('C', 16.**************), ('A', 16.***************), ('B', 18.**************)], 'sigma_min': 16.**************, 'sigma_max': 18.**************, 'ratio_volatilite': 1.***************, 'hypothese_abc_validee': False, 'plus_stable': 'C', 'plus_volatile': 'B', 'coefficients_variation': {'A': 0.****************, 'B': 0.***************, 'C': 0.****************}, 'interpretation': '❌ Hypothèse non validée. Ordre réel: C < A < B - Pattern inattendu'}
0_PLAYER: {'hierarchie': [('A', 16.***************), ('B', 17.***************), ('C', 18.***************)], 'sigma_min': 16.***************, 'sigma_max': 18.***************, 'ratio_volatilite': 1.***************, 'hypothese_abc_validee': True, 'plus_stable': 'A', 'plus_volatile': 'C', 'coefficients_variation': {'A': 0.****************, 'B': 0.***************, 'C': 0.***************}, 'interpretation': '✅ Hypothèse validée: A (naturelles) < B < C (impaires) - Stabilité décroissante attendue'}
0_TIE: {'hierarchie': [('C', 18.**************), ('B', 19.***************), ('A', 19.**************)], 'sigma_min': 18.**************, 'sigma_max': 19.**************, 'ratio_volatilite': 1.***************, 'hypothese_abc_validee': False, 'plus_stable': 'C', 'plus_volatile': 'A', 'coefficients_variation': {'A': 0.****************, 'B': 0.*****************, 'C': 0.****************}, 'interpretation': '❌ Hypothèse non validée. Ordre réel: C < B < A - Pattern inattendu'}
1_BANKER: {'hierarchie': [('C', 15.***************), ('A', 16.***************), ('B', 18.***************)], 'sigma_min': 15.***************, 'sigma_max': 18.***************, 'ratio_volatilite': 1.****************, 'hypothese_abc_validee': False, 'plus_stable': 'C', 'plus_volatile': 'B', 'coefficients_variation': {'A': 0.****************, 'B': 0.****************, 'C': 0.****************}, 'interpretation': '❌ Hypothèse non validée. Ordre réel: C < A < B - Pattern inattendu'}
1_PLAYER: {'hierarchie': [('A', 16.***************), ('B', 17.**************), ('C', 18.***************)], 'sigma_min': 16.***************, 'sigma_max': 18.***************, 'ratio_volatilite': 1.****************, 'hypothese_abc_validee': True, 'plus_stable': 'A', 'plus_volatile': 'C', 'coefficients_variation': {'A': 0.****************, 'B': 0.****************, 'C': 0.****************}, 'interpretation': '✅ Hypothèse validée: A (naturelles) < B < C (impaires) - Stabilité décroissante attendue'}
1_TIE: {'hierarchie': [('C', 18.***************), ('B', 19.***************), ('A', 19.***************)], 'sigma_min': 18.***************, 'sigma_max': 19.***************, 'ratio_volatilite': 1.****************, 'hypothese_abc_validee': False, 'plus_stable': 'C', 'plus_volatile': 'A', 'coefficients_variation': {'A': 0.****************, 'B': 0.*****************, 'C': 0.****************}, 'interpretation': '❌ Hypothèse non validée. Ordre réel: C < B < A - Pattern inattendu'}

INDEX3 PATTERNS
===============
0_A: {'sigmas': {'BANKER': 16.***************, 'PLAYER': 16.***************, 'TIE': 19.**************}, 'ratio_tie_banker': 1.****************, 'ratio_tie_player': 1.****************, 'ratio_banker_player': 0.****************, 'tie_plus_volatile': False, 'banker_player_similaires': True, 'hypothese_validee': False, 'interpretation': '⚠️ BANKER≈PLAYER confirmé, mais TIE pas plus volatile - Pattern surprenant'}
0_B: {'sigmas': {'BANKER': 18.**************, 'PLAYER': 17.***************, 'TIE': 19.***************}, 'ratio_tie_banker': 1.****************, 'ratio_tie_player': 1.****************, 'ratio_banker_player': 1.***************, 'tie_plus_volatile': False, 'banker_player_similaires': True, 'hypothese_validee': False, 'interpretation': '⚠️ BANKER≈PLAYER confirmé, mais TIE pas plus volatile - Pattern surprenant'}
0_C: {'sigmas': {'BANKER': 16.**************, 'PLAYER': 18.***************, 'TIE': 18.**************}, 'ratio_tie_banker': 1.****************, 'ratio_tie_player': 0.****************, 'ratio_banker_player': 0.****************, 'tie_plus_volatile': False, 'banker_player_similaires': True, 'hypothese_validee': False, 'interpretation': '⚠️ BANKER≈PLAYER confirmé, mais TIE pas plus volatile - Pattern surprenant'}
1_A: {'sigmas': {'BANKER': 16.***************, 'PLAYER': 16.***************, 'TIE': 19.***************}, 'ratio_tie_banker': 1.****************, 'ratio_tie_player': 1.****************, 'ratio_banker_player': 0.**************, 'tie_plus_volatile': False, 'banker_player_similaires': True, 'hypothese_validee': False, 'interpretation': '⚠️ BANKER≈PLAYER confirmé, mais TIE pas plus volatile - Pattern surprenant'}
1_B: {'sigmas': {'BANKER': 18.***************, 'PLAYER': 17.**************, 'TIE': 19.***************}, 'ratio_tie_banker': 1.****************, 'ratio_tie_player': 1.****************, 'ratio_banker_player': 1.***************, 'tie_plus_volatile': False, 'banker_player_similaires': True, 'hypothese_validee': False, 'interpretation': '⚠️ BANKER≈PLAYER confirmé, mais TIE pas plus volatile - Pattern surprenant'}
1_C: {'sigmas': {'BANKER': 15.***************, 'PLAYER': 18.***************, 'TIE': 18.***************}, 'ratio_tie_banker': 1.****************, 'ratio_tie_player': 0.****************, 'ratio_banker_player': 0.****************, 'tie_plus_volatile': False, 'banker_player_similaires': True, 'hypothese_validee': False, 'interpretation': '⚠️ BANKER≈PLAYER confirmé, mais TIE pas plus volatile - Pattern surprenant'}

ANOMALIES
=========

STABILITÉ
=========
0_A_BANKER: {'ratio_sharpe': 1.***************, 'classe_stabilite': 'STABLE', 'avantage': 19.***************, 'ecart_type': 16.***************, 'efficacite': 'FAIBLE', 'recommandation': '🟡 ACCEPTABLE: Règle utilisable avec prudence'}
1_A_BANKER: {'ratio_sharpe': 1.****************, 'classe_stabilite': 'STABLE', 'avantage': 19.**************, 'ecart_type': 16.***************, 'efficacite': 'FAIBLE', 'recommandation': '🟡 ACCEPTABLE: Règle utilisable avec prudence'}
0_B_BANKER: {'ratio_sharpe': 1.****************, 'classe_stabilite': 'STABLE', 'avantage': 23.***************, 'ecart_type': 18.**************, 'efficacite': 'FAIBLE', 'recommandation': '🟡 ACCEPTABLE: Règle utilisable avec prudence'}
1_B_BANKER: {'ratio_sharpe': 1.***************, 'classe_stabilite': 'STABLE', 'avantage': 23.***************, 'ecart_type': 18.***************, 'efficacite': 'FAIBLE', 'recommandation': '🟡 ACCEPTABLE: Règle utilisable avec prudence'}
0_C_BANKER: {'ratio_sharpe': 1.****************, 'classe_stabilite': 'STABLE', 'avantage': 20.**************, 'ecart_type': 16.**************, 'efficacite': 'FAIBLE', 'recommandation': '🟡 ACCEPTABLE: Règle utilisable avec prudence'}
1_C_BANKER: {'ratio_sharpe': 1.****************, 'classe_stabilite': 'STABLE', 'avantage': 19.**************, 'ecart_type': 15.***************, 'efficacite': 'FAIBLE', 'recommandation': '🟡 ACCEPTABLE: Règle utilisable avec prudence'}
0_A_PLAYER: {'ratio_sharpe': 1.***************, 'classe_stabilite': 'STABLE', 'avantage': 19.***************, 'ecart_type': 16.***************, 'efficacite': 'FAIBLE', 'recommandation': '🟡 ACCEPTABLE: Règle utilisable avec prudence'}
1_A_PLAYER: {'ratio_sharpe': 1.****************, 'classe_stabilite': 'STABLE', 'avantage': 19.**************, 'ecart_type': 16.***************, 'efficacite': 'FAIBLE', 'recommandation': '🟡 ACCEPTABLE: Règle utilisable avec prudence'}
0_B_PLAYER: {'ratio_sharpe': 1.****************, 'classe_stabilite': 'STABLE', 'avantage': 21.***************, 'ecart_type': 17.***************, 'efficacite': 'FAIBLE', 'recommandation': '🟡 ACCEPTABLE: Règle utilisable avec prudence'}
1_B_PLAYER: {'ratio_sharpe': 1.***************, 'classe_stabilite': 'STABLE', 'avantage': 21.055071520510378, 'ecart_type': 17.**************, 'efficacite': 'FAIBLE', 'recommandation': '🟡 ACCEPTABLE: Règle utilisable avec prudence'}
0_C_PLAYER: {'ratio_sharpe': 1.2769035520713388, 'classe_stabilite': 'STABLE', 'avantage': 23.596010417349326, 'ecart_type': 18.***************, 'efficacite': 'FAIBLE', 'recommandation': '🟡 ACCEPTABLE: Règle utilisable avec prudence'}
1_C_PLAYER: {'ratio_sharpe': 1.2806265669854253, 'classe_stabilite': 'STABLE', 'avantage': 23.538847679667086, 'ecart_type': 18.***************, 'efficacite': 'FAIBLE', 'recommandation': '🟡 ACCEPTABLE: Règle utilisable avec prudence'}
0_A_TIE: {'ratio_sharpe': 1.993756605343316, 'classe_stabilite': 'STABLE', 'avantage': 38.78352361228346, 'ecart_type': 19.**************, 'efficacite': 'ELEVEE', 'recommandation': '🟡 ACCEPTABLE: Règle utilisable avec prudence'}
1_A_TIE: {'ratio_sharpe': 1.9820238114972706, 'classe_stabilite': 'STABLE', 'avantage': 38.67820927444931, 'ecart_type': 19.***************, 'efficacite': 'ELEVEE', 'recommandation': '🟡 ACCEPTABLE: Règle utilisable avec prudence'}
0_B_TIE: {'ratio_sharpe': 2.0571266609192356, 'classe_stabilite': 'TRES_STABLE', 'avantage': 39.419357635109776, 'ecart_type': 19.***************, 'efficacite': 'ELEVEE', 'recommandation': '🟢 RECOMMANDÉ: Règle très fiable pour prédiction'}
1_B_TIE: {'ratio_sharpe': 2.056451183054464, 'classe_stabilite': 'TRES_STABLE', 'avantage': 39.44037479740371, 'ecart_type': 19.***************, 'efficacite': 'ELEVEE', 'recommandation': '🟢 RECOMMANDÉ: Règle très fiable pour prédiction'}
0_C_TIE: {'ratio_sharpe': 2.2907368458520905, 'classe_stabilite': 'TRES_STABLE', 'avantage': 41.35769226782207, 'ecart_type': 18.**************, 'efficacite': 'ELEVEE', 'recommandation': '🟢 RECOMMANDÉ: Règle très fiable pour prédiction'}
1_C_TIE: {'ratio_sharpe': 2.294239138663739, 'classe_stabilite': 'TRES_STABLE', 'avantage': 41.361310033140384, 'ecart_type': 18.***************, 'efficacite': 'ELEVEE', 'recommandation': '🟢 RECOMMANDÉ: Règle très fiable pour prédiction'}
