#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Analyseur Prédictif Ultra-Avancé pour Baccarat
SYSTÈME DE DÉCISION TEMPS RÉEL BASÉ SUR 5.4M POINTS VALIDÉS

Ce module détermine précisément QUAND et COMMENT faire une prédiction S/O
avec une confiance maximale basée sur l'analyse historique de 100,000 parties.

Auteur: Expert Statisticien
Date: 2025-06-23
"""

import sys
import os
import numpy as np
from datetime import datetime
from dataclasses import dataclass
from typing import Dict, List, Tuple, Optional
from enum import Enum

# Ajouter le répertoire courant au path pour les imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class NiveauConfiance(Enum):
    """Niveaux de confiance pour les prédictions"""
    TRES_FAIBLE = "TRÈS_FAIBLE"
    FAIBLE = "FAIBLE"
    MODERE = "MODÉRÉ"
    ELEVE = "ÉLEVÉ"
    TRES_ELEVE = "TRÈS_ÉLEVÉ"
    EXCEPTIONNEL = "EXCEPTIONNEL"

class ActionRecommandee(Enum):
    """Actions recommandées basées sur l'analyse"""
    ABSTENTION = "ABSTENTION"
    OBSERVATION = "OBSERVATION"
    PREDICTION_PRUDENTE = "PRÉDICTION_PRUDENTE"
    PREDICTION_RECOMMANDEE = "PRÉDICTION_RECOMMANDÉE"
    PREDICTION_FORTE = "PRÉDICTION_FORTE"
    PREDICTION_MAXIMALE = "PRÉDICTION_MAXIMALE"

@dataclass
class ConditionPredictive:
    """Structure pour une condition prédictive"""
    nom: str
    condition: callable
    probabilite_s: float
    probabilite_o: float
    nb_cas_valides: int
    confiance: float
    poids: float
    description: str

@dataclass
class ResultatPrediction:
    """Résultat d'une prédiction complète"""
    prediction: str  # 'S' ou 'O'
    probabilite: float
    confiance_globale: float
    niveau_confiance: NiveauConfiance
    action_recommandee: ActionRecommandee
    conditions_activees: List[str]
    scores_detailles: Dict[str, float]
    justification: str
    seuil_decision: float
    marge_securite: float

@dataclass
class ResultatValidation:
    """Résultat de validation d'une prédiction"""
    prediction: str
    resultat_reel: str
    statut: str  # 'SUCCES', 'ECHEC', 'NEUTRE'
    est_valide: bool  # False si TIE (E)
    justification: str

class AnalyseurPredictifUltraAvance:
    """
    Analyseur prédictif ultra-avancé basé sur l'analyse de 5.4M points
    """
    
    def __init__(self):
        """Initialisation avec les données validées sur 100,000 parties"""
        print("🚀 ANALYSEUR PRÉDICTIF ULTRA-AVANCÉ")
        print("📊 Basé sur validation de 5,448,036 points de données")
        print("🎯 Système de décision temps réel")
        
        # Conditions prédictives validées sur 5.4M points
        self.conditions_predictives = self._initialiser_conditions_predictives()
        
        # Seuils de décision optimisés
        self.seuils_decision = {
            'prediction_minimale': 55.0,      # 55% minimum pour prédire
            'prediction_recommandee': 60.0,   # 60% pour recommander
            'prediction_forte': 65.0,         # 65% pour prédiction forte
            'prediction_maximale': 70.0,      # 70% pour prédiction maximale
            'confiance_minimale': 30.0,       # Confiance minimale globale
            'marge_securite': 5.0             # Marge de sécurité
        }
        
        # Historique des prédictions pour apprentissage
        self.historique_predictions = []
        
        print("✅ Analyseur initialisé avec conditions ultra-optimisées")
    
    def _initialiser_conditions_predictives(self) -> List[ConditionPredictive]:
        """
        Initialise les conditions prédictives basées sur l'analyse historique
        """
        conditions = []
        
        # CONDITION 1: Ordre fort L4
        conditions.append(ConditionPredictive(
            nom="ORDRE_FORT_L4",
            condition=lambda r4, r5, d4, d5: r4 < 0.5,
            probabilite_s=57.7,
            probabilite_o=42.3,
            nb_cas_valides=2061691,
            confiance=85.0,
            poids=25.0,
            description="Ratio L4 < 0.5 (ordre fort) → Continuation probable"
        ))
        
        # CONDITION 2: Ordre fort L5
        conditions.append(ConditionPredictive(
            nom="ORDRE_FORT_L5",
            condition=lambda r4, r5, d4, d5: r5 < 0.5,
            probabilite_s=58.2,
            probabilite_o=41.8,
            nb_cas_valides=903638,
            confiance=90.0,
            poids=30.0,
            description="Ratio L5 < 0.5 (ordre fort) → Continuation très probable"
        ))
        
        # CONDITION 3: Grandes variations L4
        conditions.append(ConditionPredictive(
            nom="GRANDES_VARIATIONS_L4",
            condition=lambda r4, r5, d4, d5: d4 > 0.2,
            probabilite_s=64.1,
            probabilite_o=35.9,
            nb_cas_valides=167352,
            confiance=95.0,
            poids=35.0,
            description="DIFF_L4 > 0.2 (grandes variations) → Continuation forte"
        ))
        
        # CONDITION 4: Grandes variations L5
        conditions.append(ConditionPredictive(
            nom="GRANDES_VARIATIONS_L5",
            condition=lambda r4, r5, d4, d5: d5 > 0.2,
            probabilite_s=64.8,
            probabilite_o=35.2,
            nb_cas_valides=41141,
            confiance=95.0,
            poids=35.0,
            description="DIFF_L5 > 0.2 (grandes variations) → Continuation très forte"
        ))
        
        # CONDITION 5: Incohérence L4/L5 extrême
        conditions.append(ConditionPredictive(
            nom="INCOHERENCE_EXTREME",
            condition=lambda r4, r5, d4, d5: abs(r4 - r5) > 0.2,
            probabilite_s=72.2,
            probabilite_o=27.8,
            nb_cas_valides=48142,
            confiance=98.0,
            poids=40.0,
            description="|L4-L5| > 0.2 (incohérence extrême) → Continuation exceptionnelle"
        ))
        
        # CONDITION 6: Stabilité extrême (alternance)
        conditions.append(ConditionPredictive(
            nom="STABILITE_EXTREME",
            condition=lambda r4, r5, d4, d5: d4 < 0.02 and d5 < 0.02,
            probabilite_s=45.7,
            probabilite_o=54.3,
            nb_cas_valides=3135434,
            confiance=80.0,
            poids=20.0,
            description="DIFF_L4 < 0.02 ET DIFF_L5 < 0.02 → Alternance probable"
        ))
        
        # CONDITION 7: Ordre modéré L4 (alternance)
        conditions.append(ConditionPredictive(
            nom="ORDRE_MODERE_L4",
            condition=lambda r4, r5, d4, d5: 0.5 <= r4 < 0.7,
            probabilite_s=45.4,
            probabilite_o=54.6,
            nb_cas_valides=3104273,
            confiance=75.0,
            poids=15.0,
            description="0.5 ≤ L4 < 0.7 (ordre modéré) → Alternance légère"
        ))
        
        # CONDITION 8: Combinaison ordre/chaos
        conditions.append(ConditionPredictive(
            nom="ORDRE_CHAOS_COMBO",
            condition=lambda r4, r5, d4, d5: (r4 < 0.7 and r5 > 0.9) or (r4 > 0.9 and r5 < 0.7),
            probabilite_s=65.4,
            probabilite_o=34.6,
            nb_cas_valides=697,
            confiance=85.0,
            poids=30.0,
            description="Combinaison ordre/chaos → Continuation forte"
        ))
        
        return conditions
    
    def analyser_situation_actuelle(self, ratio_l4: float, ratio_l5: float, 
                                  diff_l4: float, diff_l5: float,
                                  historique_patterns: List[str] = None) -> ResultatPrediction:
        """
        Analyse la situation actuelle et détermine la prédiction optimale
        
        Args:
            ratio_l4: Ratio entropique L4 actuel
            ratio_l5: Ratio entropique L5 actuel
            diff_l4: Différentiel L4 (variation depuis main précédente)
            diff_l5: Différentiel L5 (variation depuis main précédente)
            historique_patterns: Historique des patterns récents (optionnel)
            
        Returns:
            ResultatPrediction: Analyse complète avec recommandation
        """
        print(f"\n🔬 ANALYSE SITUATION ACTUELLE")
        print(f"📊 L4: {ratio_l4:.3f}, L5: {ratio_l5:.3f}")
        print(f"📈 DIFF_L4: {diff_l4:.3f}, DIFF_L5: {diff_l5:.3f}")
        
        # Évaluer toutes les conditions
        conditions_activees = []
        scores_s = []
        scores_o = []
        confiances = []
        poids_total = 0
        
        for condition in self.conditions_predictives:
            if condition.condition(ratio_l4, ratio_l5, diff_l4, diff_l5):
                conditions_activees.append(condition.nom)
                scores_s.append(condition.probabilite_s * condition.poids / 100)
                scores_o.append(condition.probabilite_o * condition.poids / 100)
                confiances.append(condition.confiance * condition.poids / 100)
                poids_total += condition.poids
                
                print(f"✅ {condition.nom}: {condition.probabilite_s:.1f}% S (poids: {condition.poids})")
        
        if not conditions_activees:
            return self._generer_prediction_neutre()
        
        # Calculer les scores pondérés
        score_s_total = sum(scores_s)
        score_o_total = sum(scores_o)
        confiance_globale = sum(confiances) / len(confiances) if confiances else 0
        
        # Normaliser les scores
        score_total = score_s_total + score_o_total
        if score_total > 0:
            probabilite_s = score_s_total / score_total * 100
            probabilite_o = score_o_total / score_total * 100
        else:
            probabilite_s = probabilite_o = 50.0
        
        # Déterminer la prédiction
        prediction = 'S' if probabilite_s > probabilite_o else 'O'
        probabilite_max = max(probabilite_s, probabilite_o)
        
        # Calculer la marge de sécurité
        marge_securite = abs(probabilite_s - probabilite_o)
        
        # Déterminer le niveau de confiance
        niveau_confiance = self._determiner_niveau_confiance(probabilite_max, confiance_globale, marge_securite)
        
        # Déterminer l'action recommandée
        action_recommandee = self._determiner_action_recommandee(probabilite_max, confiance_globale, marge_securite)
        
        # Générer la justification
        justification = self._generer_justification(conditions_activees, prediction, probabilite_max)
        
        # Créer le résultat
        resultat = ResultatPrediction(
            prediction=prediction,
            probabilite=probabilite_max,
            confiance_globale=confiance_globale,
            niveau_confiance=niveau_confiance,
            action_recommandee=action_recommandee,
            conditions_activees=conditions_activees,
            scores_detailles={
                'score_s': probabilite_s,
                'score_o': probabilite_o,
                'poids_total': poids_total,
                'nb_conditions': len(conditions_activees)
            },
            justification=justification,
            seuil_decision=self.seuils_decision['prediction_minimale'],
            marge_securite=marge_securite
        )
        
        # Ajouter à l'historique
        self.historique_predictions.append(resultat)

        return resultat

    def valider_prediction(self, prediction: str, resultat_reel: str) -> ResultatValidation:
        """
        Valide une prédiction en tenant compte des TIE (E)

        Args:
            prediction: Prédiction faite ('S' ou 'O')
            resultat_reel: Résultat réel ('S', 'O', ou 'E')

        Returns:
            ResultatValidation: Résultat de la validation
        """
        # CAS SPÉCIAL : TIE (E) = NEUTRE
        if resultat_reel == 'E':
            return ResultatValidation(
                prediction=prediction,
                resultat_reel=resultat_reel,
                statut='NEUTRE',
                est_valide=False,  # Ne compte pas dans les statistiques
                justification='TIE (E) - Ni succès ni échec'
            )

        # CAS NORMAUX : S ou O
        if prediction == resultat_reel:
            return ResultatValidation(
                prediction=prediction,
                resultat_reel=resultat_reel,
                statut='SUCCES',
                est_valide=True,
                justification=f'Prédiction correcte: {prediction} → {resultat_reel}'
            )
        else:
            return ResultatValidation(
                prediction=prediction,
                resultat_reel=resultat_reel,
                statut='ECHEC',
                est_valide=True,
                justification=f'Prédiction incorrecte: {prediction} → {resultat_reel}'
            )

    def _generer_prediction_neutre(self) -> ResultatPrediction:
        """Génère une prédiction neutre quand aucune condition n'est activée"""
        return ResultatPrediction(
            prediction='NEUTRE',
            probabilite=50.0,
            confiance_globale=0.0,
            niveau_confiance=NiveauConfiance.TRES_FAIBLE,
            action_recommandee=ActionRecommandee.ABSTENTION,
            conditions_activees=[],
            scores_detailles={'score_s': 50.0, 'score_o': 50.0, 'poids_total': 0, 'nb_conditions': 0},
            justification="Aucune condition prédictive activée - Situation neutre",
            seuil_decision=50.0,
            marge_securite=0.0
        )

    def _determiner_niveau_confiance(self, probabilite: float, confiance_globale: float, marge_securite: float) -> NiveauConfiance:
        """Détermine le niveau de confiance basé sur les métriques"""
        score_confiance = (probabilite - 50) + (confiance_globale / 2) + (marge_securite / 2)

        if score_confiance >= 25:
            return NiveauConfiance.EXCEPTIONNEL
        elif score_confiance >= 20:
            return NiveauConfiance.TRES_ELEVE
        elif score_confiance >= 15:
            return NiveauConfiance.ELEVE
        elif score_confiance >= 10:
            return NiveauConfiance.MODERE
        elif score_confiance >= 5:
            return NiveauConfiance.FAIBLE
        else:
            return NiveauConfiance.TRES_FAIBLE

    def _determiner_action_recommandee(self, probabilite: float, confiance_globale: float, marge_securite: float) -> ActionRecommandee:
        """Détermine l'action recommandée basée sur les seuils optimisés"""
        if probabilite >= self.seuils_decision['prediction_maximale'] and confiance_globale >= 90 and marge_securite >= 20:
            return ActionRecommandee.PREDICTION_MAXIMALE
        elif probabilite >= self.seuils_decision['prediction_forte'] and confiance_globale >= 80 and marge_securite >= 15:
            return ActionRecommandee.PREDICTION_FORTE
        elif probabilite >= self.seuils_decision['prediction_recommandee'] and confiance_globale >= 70 and marge_securite >= 10:
            return ActionRecommandee.PREDICTION_RECOMMANDEE
        elif probabilite >= self.seuils_decision['prediction_minimale'] and confiance_globale >= 50 and marge_securite >= 5:
            return ActionRecommandee.PREDICTION_PRUDENTE
        elif probabilite >= 52 and confiance_globale >= 30:
            return ActionRecommandee.OBSERVATION
        else:
            return ActionRecommandee.ABSTENTION

    def _generer_justification(self, conditions_activees: List[str], prediction: str, probabilite: float) -> str:
        """Génère une justification détaillée pour la prédiction"""
        if not conditions_activees:
            return "Aucune condition prédictive activée"

        justification = f"Prédiction {prediction} ({probabilite:.1f}%) basée sur :\n"

        for condition_nom in conditions_activees:
            condition = next(c for c in self.conditions_predictives if c.nom == condition_nom)
            justification += f"• {condition.description}\n"

        return justification.strip()

    def analyser_sequence_optimale(self, historique_ratios: List[Tuple[float, float]],
                                 historique_diffs: List[Tuple[float, float]]) -> Dict:
        """
        Analyse une séquence complète pour identifier les moments optimaux de prédiction

        Args:
            historique_ratios: Liste des (ratio_l4, ratio_l5) pour chaque main
            historique_diffs: Liste des (diff_l4, diff_l5) pour chaque main

        Returns:
            Dict: Analyse complète de la séquence avec moments optimaux
        """
        print(f"\n🔬 ANALYSE SÉQUENCE OPTIMALE")
        print(f"📊 {len(historique_ratios)} mains à analyser")

        moments_optimaux = []
        predictions_sequence = []

        for i, ((r4, r5), (d4, d5)) in enumerate(zip(historique_ratios, historique_diffs)):
            resultat = self.analyser_situation_actuelle(r4, r5, d4, d5)
            predictions_sequence.append(resultat)

            # Identifier les moments optimaux (action recommandée forte)
            if resultat.action_recommandee in [ActionRecommandee.PREDICTION_FORTE, ActionRecommandee.PREDICTION_MAXIMALE]:
                moments_optimaux.append({
                    'main': i + 6,  # Commence à main 6
                    'prediction': resultat.prediction,
                    'probabilite': resultat.probabilite,
                    'confiance': resultat.confiance_globale,
                    'action': resultat.action_recommandee.value,
                    'conditions': resultat.conditions_activees,
                    'justification': resultat.justification
                })

        # Statistiques de la séquence
        nb_predictions_fortes = len(moments_optimaux)
        taux_prediction = (nb_predictions_fortes / len(predictions_sequence)) * 100 if predictions_sequence else 0

        # Analyse des patterns de prédiction
        predictions_s = sum(1 for m in moments_optimaux if m['prediction'] == 'S')
        predictions_o = sum(1 for m in moments_optimaux if m['prediction'] == 'O')

        return {
            'moments_optimaux': moments_optimaux,
            'nb_predictions_fortes': nb_predictions_fortes,
            'taux_prediction': taux_prediction,
            'predictions_s': predictions_s,
            'predictions_o': predictions_o,
            'predictions_sequence': predictions_sequence,
            'statistiques': {
                'probabilite_moyenne': np.mean([m['probabilite'] for m in moments_optimaux]) if moments_optimaux else 0,
                'confiance_moyenne': np.mean([m['confiance'] for m in moments_optimaux]) if moments_optimaux else 0,
                'conditions_plus_frequentes': self._analyser_conditions_frequentes(moments_optimaux)
            }
        }

    def _analyser_conditions_frequentes(self, moments_optimaux: List[Dict]) -> Dict[str, int]:
        """Analyse les conditions les plus fréquemment activées"""
        compteur_conditions = {}

        for moment in moments_optimaux:
            for condition in moment['conditions']:
                compteur_conditions[condition] = compteur_conditions.get(condition, 0) + 1

        return dict(sorted(compteur_conditions.items(), key=lambda x: x[1], reverse=True))

    def generer_rapport_predictif(self, nom_fichier: str = None) -> str:
        """
        Génère un rapport détaillé du système prédictif

        Args:
            nom_fichier: Nom du fichier de rapport (optionnel)

        Returns:
            str: Chemin du fichier généré
        """
        if nom_fichier is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            nom_fichier = f"rapport_predictif_ultra_avance_{timestamp}.txt"

        with open(nom_fichier, 'w', encoding='utf-8') as f:
            f.write("RAPPORT SYSTÈME PRÉDICTIF ULTRA-AVANCÉ\n")
            f.write("=" * 60 + "\n\n")

            f.write(f"Date de génération: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Basé sur validation de 5,448,036 points de données\n")
            f.write(f"Analyseur: Ultra-Avancé avec 8 conditions prédictives\n\n")

            # Conditions prédictives
            f.write("CONDITIONS PRÉDICTIVES VALIDÉES\n")
            f.write("=" * 40 + "\n\n")

            for i, condition in enumerate(self.conditions_predictives, 1):
                f.write(f"{i}. {condition.nom}\n")
                f.write(f"   Condition: {condition.description}\n")
                f.write(f"   Probabilité S: {condition.probabilite_s:.1f}%\n")
                f.write(f"   Probabilité O: {condition.probabilite_o:.1f}%\n")
                f.write(f"   Cas validés: {condition.nb_cas_valides:,}\n")
                f.write(f"   Confiance: {condition.confiance:.1f}%\n")
                f.write(f"   Poids: {condition.poids:.1f}\n\n")

            # Seuils de décision
            f.write("SEUILS DE DÉCISION OPTIMISÉS\n")
            f.write("=" * 35 + "\n\n")

            for seuil, valeur in self.seuils_decision.items():
                f.write(f"{seuil.replace('_', ' ').title()}: {valeur:.1f}%\n")

            f.write("\n")

            # Guide d'utilisation
            f.write("GUIDE D'UTILISATION DU SYSTÈME\n")
            f.write("=" * 35 + "\n\n")

            f.write("1. COLLECTE DES DONNÉES:\n")
            f.write("   - Ratio L4 actuel\n")
            f.write("   - Ratio L5 actuel\n")
            f.write("   - DIFF_L4 (variation depuis main précédente)\n")
            f.write("   - DIFF_L5 (variation depuis main précédente)\n\n")

            f.write("2. ANALYSE AUTOMATIQUE:\n")
            f.write("   - Évaluation de toutes les conditions\n")
            f.write("   - Calcul des scores pondérés\n")
            f.write("   - Détermination du niveau de confiance\n")
            f.write("   - Recommandation d'action\n\n")

            f.write("3. ACTIONS RECOMMANDÉES:\n")
            f.write("   - ABSTENTION: Aucune prédiction (< 55%)\n")
            f.write("   - OBSERVATION: Surveiller (52-55%)\n")
            f.write("   - PRÉDICTION_PRUDENTE: Prédiction faible (55-60%)\n")
            f.write("   - PRÉDICTION_RECOMMANDÉE: Prédiction standard (60-65%)\n")
            f.write("   - PRÉDICTION_FORTE: Prédiction élevée (65-70%)\n")
            f.write("   - PRÉDICTION_MAXIMALE: Prédiction exceptionnelle (>70%)\n\n")

            # Historique des prédictions
            if self.historique_predictions:
                f.write("HISTORIQUE DES PRÉDICTIONS\n")
                f.write("=" * 30 + "\n\n")

                for i, pred in enumerate(self.historique_predictions[-10:], 1):  # 10 dernières
                    f.write(f"Prédiction {i}:\n")
                    f.write(f"   Résultat: {pred.prediction} ({pred.probabilite:.1f}%)\n")
                    f.write(f"   Confiance: {pred.confiance_globale:.1f}%\n")
                    f.write(f"   Action: {pred.action_recommandee.value}\n")
                    f.write(f"   Conditions: {', '.join(pred.conditions_activees)}\n\n")

        print(f"✅ Rapport généré: {nom_fichier}")
        return nom_fichier
