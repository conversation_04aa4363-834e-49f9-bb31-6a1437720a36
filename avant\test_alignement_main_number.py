#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test de l'alignement main_number = index
Vérifie que la modification fonctionne correctement
"""

import json
from analyseur_transitions_index5 import AnalyseurTransitionsIndex5

def test_alignement_basique():
    """Test basique de l'alignement avec données simulées"""
    
    print("🧪 TEST DE L'ALIGNEMENT main_number = index")
    print("=" * 50)
    
    # Créer des données de test
    mains_test = [
        {'index5_combined': '1_A_PLAYER', 'main_number': 1},
        {'index5_combined': '1_B_BANKER', 'main_number': 2},
        {'index5_combined': '0_C_PLAYER', 'main_number': 3},
        {'index5_combined': '1_A_BANKER', 'main_number': 4},
        {'index5_combined': '0_B_PLAYER', 'main_number': 5},
        {'index5_combined': '1_C_BANKER', 'main_number': 6},
    ]
    
    print("AVANT alignement :")
    for i, main in enumerate(mains_test):
        print(f"  Index {i} → main_number {main['main_number']} → {main['index5_combined']}")
    
    # Simuler l'alignement
    main_dummy = {'index5_combined': 'IGNORE_MAIN_0'}
    mains_alignees = [main_dummy] + mains_test
    sequence_complete = [main['index5_combined'] for main in mains_alignees]
    
    print("\nAPRÈS alignement :")
    for i, index5 in enumerate(sequence_complete):
        if i == 0:
            print(f"  Index {i} → main_number {i} → {index5} (IGNORÉ)")
        else:
            print(f"  Index {i} → main_number {i} → {index5}")
    
    print("\nVérification des séquences L4 et L5 pour main 5 :")
    position_main = 5
    
    # L4 : [main 2,3,4,5]
    seq_4 = sequence_complete[position_main-3:position_main+1]
    print(f"  L4 (mains 2,3,4,5) : {seq_4}")
    
    # L5 : [main 1,2,3,4,5]  
    seq_5 = sequence_complete[position_main-4:position_main+1]
    print(f"  L5 (mains 1,2,3,4,5) : {seq_5}")
    
    # Vérification de l'accès direct
    main_5_value = sequence_complete[5]
    print(f"  Accès direct main 5 : sequence_complete[5] = {main_5_value}")
    
    return True

def test_avec_vraies_donnees():
    """Test avec les vraies données du fichier JSON"""
    
    print("\n🔍 TEST AVEC VRAIES DONNÉES")
    print("=" * 50)
    
    try:
        # Charger les vraies données
        with open('dataset_baccarat_lupasco_20250623_080828.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        premiere_partie = data['parties'][0]
        mains = premiere_partie['mains'][:10]  # Prendre les 10 premières mains
        
        print("DONNÉES ORIGINALES :")
        for i, main in enumerate(mains):
            print(f"  Index {i} → main_number {main['main_number']} → {main['index5_combined']}")
        
        # Simuler l'alignement
        main_dummy = {'index5_combined': 'IGNORE_MAIN_0'}
        mains_alignees = [main_dummy] + mains
        sequence_complete = [main['index5_combined'] for main in mains_alignees]
        
        print("\nAPRÈS ALIGNEMENT :")
        for i, index5 in enumerate(sequence_complete):
            if i == 0:
                print(f"  Index {i} → main_number {i} → {index5} (IGNORÉ)")
            elif i <= len(mains):
                original_main_number = mains[i-1]['main_number']
                print(f"  Index {i} → main_number {i} → {index5} (était main_number {original_main_number})")
        
        print(f"\n✅ Alignement réussi : {len(sequence_complete)} éléments")
        print(f"   Index 0 = main 0 (ignoré)")
        print(f"   Index 1 = main 1 (première vraie main)")
        print(f"   Index 5 = main 5 (cinquième vraie main)")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test avec vraies données : {e}")
        return False

def test_sequences_l4_l5():
    """Test spécifique des séquences L4 et L5"""
    
    print("\n🎯 TEST DES SÉQUENCES L4 ET L5")
    print("=" * 50)
    
    # Créer une séquence de test
    sequence_test = [
        'IGNORE_MAIN_0',  # Index 0 - main 0 (ignoré)
        '1_A_PLAYER',     # Index 1 - main 1
        '1_B_BANKER',     # Index 2 - main 2
        '0_C_PLAYER',     # Index 3 - main 3
        '1_A_BANKER',     # Index 4 - main 4
        '0_B_PLAYER',     # Index 5 - main 5
        '1_C_BANKER',     # Index 6 - main 6
    ]
    
    print("Séquence complète :")
    for i, val in enumerate(sequence_test):
        status = "(IGNORÉ)" if i == 0 else ""
        print(f"  Index {i} = main {i} : {val} {status}")
    
    print("\nAnalyse de la main 5 :")
    position_main = 5
    
    # L4 pour main 5 : [main 2,3,4,5]
    seq_l4 = sequence_test[position_main-3:position_main+1]
    print(f"  L4 (mains 2,3,4,5) : {seq_l4}")
    print(f"      Indices utilisés : [{position_main-3}:{position_main+1}] = [2:6]")
    
    # L5 pour main 5 : [main 1,2,3,4,5]
    seq_l5 = sequence_test[position_main-4:position_main+1]
    print(f"  L5 (mains 1,2,3,4,5) : {seq_l5}")
    print(f"      Indices utilisés : [{position_main-4}:{position_main+1}] = [1:6]")
    
    # Vérification que main 0 n'est jamais incluse
    print(f"\n✅ Vérification : main 0 n'est jamais incluse dans les séquences")
    print(f"   L4 commence à l'index 2 (main 2)")
    print(f"   L5 commence à l'index 1 (main 1)")
    print(f"   Index 0 (main 0) est toujours ignoré")
    
    return True

if __name__ == "__main__":
    print("🚀 TESTS DE L'ALIGNEMENT main_number = index")
    print("=" * 60)
    
    # Exécuter tous les tests
    tests = [
        ("Test basique", test_alignement_basique),
        ("Test avec vraies données", test_avec_vraies_donnees),
        ("Test séquences L4/L5", test_sequences_l4_l5),
    ]
    
    resultats = []
    for nom_test, fonction_test in tests:
        try:
            resultat = fonction_test()
            resultats.append((nom_test, resultat))
            print(f"\n{'✅' if resultat else '❌'} {nom_test} : {'RÉUSSI' if resultat else 'ÉCHOUÉ'}")
        except Exception as e:
            resultats.append((nom_test, False))
            print(f"\n❌ {nom_test} : ERREUR - {e}")
    
    # Résumé final
    print("\n" + "=" * 60)
    print("📊 RÉSUMÉ DES TESTS")
    reussites = sum(1 for _, resultat in resultats if resultat)
    total = len(resultats)
    
    for nom_test, resultat in resultats:
        print(f"  {'✅' if resultat else '❌'} {nom_test}")
    
    print(f"\n🎯 RÉSULTAT GLOBAL : {reussites}/{total} tests réussis")
    
    if reussites == total:
        print("🎉 TOUS LES TESTS SONT RÉUSSIS !")
        print("✅ L'alignement main_number = index fonctionne correctement")
    else:
        print("⚠️ Certains tests ont échoué - vérification nécessaire")
