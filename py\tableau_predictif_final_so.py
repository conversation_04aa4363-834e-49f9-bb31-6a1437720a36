#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Tableau Prédictif Final S/O - Synthèse Complète
FUSION DES DEUX ANALYSES EXHAUSTIVES

Ce script génère le tableau prédictif final à deux colonnes (S et O)
en fusionnant toutes les conditions identifiées dans nos analyses.

Basé sur :
- Analyse 1 : tableau_predictif_so_20250623_133052.txt
- Analyse 2 : tableau_predictif_avec_diff_20250623_134418.txt

Auteur: Expert Statisticien
Date: 2025-06-23
"""

from datetime import datetime

def generer_tableau_predictif_final():
    """
    Génère le tableau prédictif final S/O avec toutes les conditions
    """
    print("📊 GÉNÉRATION TABLEAU PRÉDICTIF FINAL S/O")
    print("🔄 Fusion des deux analyses exhaustives")
    print("=" * 60)
    
    # CONDITIONS S (CONTINUATION) - FUSION DES DEUX ANALYSES
    conditions_s = [
        # TOP CONDITIONS S - ANALYSE AVEC DIFF
        {"nom": "DIFF_SIGNAL_INUTILISABLE", "condition": "DIFF > 0.300", "probabilite": 78.2, "cas": 4138, "source": "DIFF"},
        {"nom": "COMB_VARIATIONS_FORTES_DIFF_DOUTEUX", "condition": "(DIFF_L4 > 0.1 OU DIFF_L5 > 0.1) ET DIFF > 0.150", "probabilite": 72.1, "cas": 276323, "source": "DIFF"},
        {"nom": "DIFF_SIGNAL_TRÈS_DOUTEUX", "condition": "0.200 ≤ DIFF < 0.300", "probabilite": 71.6, "cas": 44009, "source": "DIFF"},
        {"nom": "COMB_ORDRE_FORT_DIFF_DOUTEUX", "condition": "L4 < 0.5 ET DIFF > 0.150", "probabilite": 70.5, "cas": 223501, "source": "DIFF"},
        
        # TOP CONDITIONS S - ANALYSE STANDARD
        {"nom": "INCOH_TRÈS_INCOHÉRENT", "condition": "|L4-L5| > 0.5", "probabilite": 98.4, "cas": 122, "source": "STANDARD"},
        {"nom": "COMPLEX_INCOH_FORTE_VAR", "condition": "|L4-L5| > 0.2 ET (DIFF_L4 > 0.1 OU DIFF_L5 > 0.1)", "probabilite": 75.9, "cas": 43120, "source": "STANDARD"},
        {"nom": "INCOH_INCOHÉRENT", "condition": "0.2 ≤ |L4-L5| < 0.5", "probabilite": 72.1, "cas": 48025, "source": "STANDARD"},
        {"nom": "L5_ORDRE_TRÈS_FORT", "condition": "L5 < 0.3", "probabilite": 68.8, "cas": 96873, "source": "STANDARD"},
        {"nom": "DIFF_L5_VAR_EXTRÊME", "condition": "DIFF_L5 > 0.5", "probabilite": 68.6, "cas": 153, "source": "STANDARD"},
        {"nom": "L4_ORDRE_TRÈS_FORT", "condition": "L4 < 0.3", "probabilite": 68.5, "cas": 334525, "source": "STANDARD"},
        {"nom": "COMB_ORDRE_FORT_DIFF_EXCELLENT", "condition": "L4 < 0.5 ET 0.020 ≤ DIFF < 0.030", "probabilite": 68.0, "cas": 28193, "source": "DIFF"},
        {"nom": "COMPLEX_ORDRE_FORT_VARIABLE", "condition": "L4 < 0.5 ET DIFF_L4 > 0.1", "probabilite": 65.8, "cas": 1054338, "source": "STANDARD"},
        {"nom": "COMB_ORDRE_CHAOS", "condition": "L4 < 0.7 ET L5 > 0.9", "probabilite": 65.4, "cas": 697, "source": "STANDARD"},
        {"nom": "DIFF_L5_TRÈS_FORTE_VAR", "condition": "0.2 ≤ DIFF_L5 < 0.5", "probabilite": 64.8, "cas": 41005, "source": "STANDARD"},
        {"nom": "DIFF_L4_TRÈS_FORTE_VAR", "condition": "0.2 ≤ DIFF_L4 < 0.5", "probabilite": 64.1, "cas": 166976, "source": "STANDARD"},
        {"nom": "DIFF_SIGNAL_DOUTEUX", "condition": "0.150 ≤ DIFF < 0.200", "probabilite": 64.1, "cas": 310756, "source": "DIFF"},
        {"nom": "DIFF_L4_VAR_EXTRÊME", "condition": "DIFF_L4 > 0.5", "probabilite": 63.6, "cas": 393, "source": "STANDARD"},
        
        # CONDITIONS S MODÉRÉES
        {"nom": "DIFF_L4_FORTE_VAR", "condition": "0.1 ≤ DIFF_L4 < 0.2", "probabilite": 57.5, "cas": 1702763, "source": "STANDARD"},
        {"nom": "L5_ORDRE_FORT", "condition": "0.3 ≤ L5 < 0.5", "probabilite": 56.9, "cas": 806765, "source": "STANDARD"},
        {"nom": "DIFF_L5_FORTE_VAR", "condition": "0.1 ≤ DIFF_L5 < 0.2", "probabilite": 55.8, "cas": 1992317, "source": "STANDARD"},
        {"nom": "L4_ORDRE_FORT", "condition": "0.3 ≤ L4 < 0.5", "probabilite": 55.6, "cas": 1727166, "source": "STANDARD"},
        {"nom": "INCOH_PEU_COHÉRENT", "condition": "0.1 ≤ |L4-L5| < 0.2", "probabilite": 55.4, "cas": 2110687, "source": "STANDARD"}
    ]
    
    # CONDITIONS O (ALTERNANCE) - FUSION DES DEUX ANALYSES
    conditions_o = [
        # TOP CONDITIONS O - ANALYSE AVEC DIFF
        {"nom": "DIFF_SIGNAL_ACCEPTABLE", "condition": "0.075 ≤ DIFF < 0.100", "probabilite": 55.9, "cas": 1772432, "source": "DIFF"},
        {"nom": "COMB_CHAOS_DIFF_DOUTEUX", "condition": "L4 > 0.9 ET DIFF > 0.150", "probabilite": 55.4, "cas": 2983, "source": "DIFF"},
        {"nom": "COMB_STABILITÉ_DIFF_EXCELLENT", "condition": "DIFF_L4 < 0.02 ET DIFF_L5 < 0.02 ET 0.020 ≤ DIFF < 0.030", "probabilite": 55.1, "cas": 75754, "source": "DIFF"},
        
        # TOP CONDITIONS O - ANALYSE STANDARD
        {"nom": "COMPLEX_CHAOS_VARIABLE", "condition": "L4 > 1.0 ET DIFF_L4 > 0.1", "probabilite": 56.2, "cas": 1859, "source": "STANDARD"},
        {"nom": "INCOH_MODÉRÉMENT_COHÉRENT", "condition": "0.05 ≤ |L4-L5| < 0.1", "probabilite": 55.7, "cas": 1796892, "source": "STANDARD"},
        {"nom": "DIFF_L4_FAIBLE_VAR", "condition": "0.02 ≤ DIFF_L4 < 0.05", "probabilite": 55.5, "cas": 296476, "source": "STANDARD"},
        {"nom": "COMPLEX_CHAOS_STABLE", "condition": "L4 > 1.0 ET DIFF_L4 < 0.02", "probabilite": 55.3, "cas": 844, "source": "STANDARD"},
        {"nom": "COMB_CHAOS_CHAOS", "condition": "L4 > 0.9 ET L5 > 0.9", "probabilite": 55.2, "cas": 15226, "source": "STANDARD"},
        {"nom": "DIFF_L4_STABLE", "condition": "0.01 ≤ DIFF_L4 < 0.02", "probabilite": 55.2, "cas": 231445, "source": "STANDARD"},
        {"nom": "COMB_ÉQUILIBRE_ÉQUILIBRE", "condition": "0.7 ≤ L4 ≤ 0.9 ET 0.7 ≤ L5 ≤ 0.9", "probabilite": 55.1, "cas": 177335, "source": "STANDARD"},
        
        # CONDITIONS O FAIBLES
        {"nom": "L4_CHAOS_MODÉRÉ", "condition": "0.9 ≤ L4 < 1.1", "probabilite": 55.0, "cas": 18924, "source": "STANDARD"},
        {"nom": "L4_CHAOS_FORT", "condition": "1.1 ≤ L4 < 1.5", "probabilite": 54.7, "cas": 1278, "source": "STANDARD"},
        {"nom": "L4_ORDRE_MODÉRÉ", "condition": "0.5 ≤ L4 < 0.7", "probabilite": 54.6, "cas": 3104273, "source": "STANDARD"},
        {"nom": "DIFF_L5_STABLE", "condition": "0.01 ≤ DIFF_L5 < 0.02", "probabilite": 54.5, "cas": 202508, "source": "STANDARD"},
        {"nom": "DIFF_L4_TRÈS_STABLE", "condition": "DIFF_L4 < 0.01", "probabilite": 54.2, "cas": 2903989, "source": "STANDARD"},
        {"nom": "DIFF_L5_VAR_MODÉRÉE", "condition": "0.05 ≤ DIFF_L5 < 0.1", "probabilite": 54.2, "cas": 270870, "source": "STANDARD"},
        {"nom": "L5_CHAOS_MODÉRÉ", "condition": "0.9 ≤ L5 < 1.1", "probabilite": 54.1, "cas": 73339, "source": "STANDARD"},
        {"nom": "L5_ÉQUILIBRE", "condition": "0.7 ≤ L5 ≤ 0.9", "probabilite": 53.7, "cas": 698750, "source": "STANDARD"},
        {"nom": "DIFF_L5_TRÈS_STABLE", "condition": "DIFF_L5 < 0.01", "probabilite": 53.7, "cas": 2592849, "source": "STANDARD"},
        {"nom": "DIFF_SIGNAL_EXCELLENT", "condition": "0.020 ≤ DIFF < 0.030", "probabilite": 54.0, "cas": 846057, "source": "DIFF"}
    ]
    
    # Trier par probabilité décroissante
    conditions_s_triees = sorted(conditions_s, key=lambda x: x['probabilite'], reverse=True)
    conditions_o_triees = sorted(conditions_o, key=lambda x: x['probabilite'], reverse=True)
    
    # Génération du tableau final
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    nom_fichier = f"tableau_predictif_final_so_{timestamp}.txt"
    
    with open(nom_fichier, 'w', encoding='utf-8') as f:
        f.write("TABLEAU PRÉDICTIF FINAL S/O - SYNTHÈSE COMPLÈTE\n")
        f.write("=" * 80 + "\n\n")
        f.write("FUSION DES DEUX ANALYSES EXHAUSTIVES SUR 100,000 PARTIES\n")
        f.write("Données analysées : 5,448,036 points\n")
        f.write("Variables incluses : DIFF, DIFF_L4, DIFF_L5, L4, L5, Combinaisons\n\n")
        
        f.write("DÉFINITIONS :\n")
        f.write("- DIFF = |L4-L5| (cohérence entre ratios L4 et L5)\n")
        f.write("- DIFF_L4 = |L4(n) - L4(n-1)| (variation L4 consécutive)\n")
        f.write("- DIFF_L5 = |L5(n) - L5(n-1)| (variation L5 consécutive)\n")
        f.write("- L4/L5 = Ratios entropiques locaux/globaux\n\n")
        
        # TABLEAU À DEUX COLONNES
        f.write("TABLEAU PRÉDICTIF À DEUX COLONNES\n")
        f.write("=" * 80 + "\n\n")
        
        # En-têtes
        f.write("CONDITIONS POUR PRÉDIRE S (CONTINUATION)".ljust(40))
        f.write(" | ")
        f.write("CONDITIONS POUR PRÉDIRE O (ALTERNANCE)".ljust(40))
        f.write("\n")
        f.write("=" * 40 + " | " + "=" * 40 + "\n")
        
        # Lignes du tableau
        max_lignes = max(len(conditions_s_triees), len(conditions_o_triees))
        
        for i in range(max_lignes):
            # Colonne S
            if i < len(conditions_s_triees):
                cond_s = conditions_s_triees[i]
                texte_s = f"{cond_s['nom'][:35]:<35} {cond_s['probabilite']:>4.1f}%"
            else:
                texte_s = " " * 40
            
            # Colonne O
            if i < len(conditions_o_triees):
                cond_o = conditions_o_triees[i]
                texte_o = f"{cond_o['nom'][:35]:<35} {cond_o['probabilite']:>4.1f}%"
            else:
                texte_o = " " * 40
            
            f.write(f"{texte_s} | {texte_o}\n")
        
        f.write("\n" + "=" * 80 + "\n\n")
        
        # DÉTAILS CONDITIONS S
        f.write("DÉTAILS CONDITIONS S (CONTINUATION)\n")
        f.write("=" * 50 + "\n\n")
        
        for i, cond in enumerate(conditions_s_triees, 1):
            f.write(f"{i:2d}. {cond['nom']}\n")
            f.write(f"    Condition: {cond['condition']}\n")
            f.write(f"    Probabilité S: {cond['probabilite']:.1f}%\n")
            f.write(f"    Cas validés: {cond['cas']:,}\n")
            f.write(f"    Source: {cond['source']}\n\n")
        
        # DÉTAILS CONDITIONS O
        f.write("DÉTAILS CONDITIONS O (ALTERNANCE)\n")
        f.write("=" * 50 + "\n\n")
        
        for i, cond in enumerate(conditions_o_triees, 1):
            f.write(f"{i:2d}. {cond['nom']}\n")
            f.write(f"    Condition: {cond['condition']}\n")
            f.write(f"    Probabilité O: {cond['probabilite']:.1f}%\n")
            f.write(f"    Cas validés: {cond['cas']:,}\n")
            f.write(f"    Source: {cond['source']}\n\n")
        
        # STATISTIQUES FINALES
        f.write("STATISTIQUES FINALES\n")
        f.write("=" * 30 + "\n\n")
        
        # Conditions par niveau
        conditions_s_fortes = [c for c in conditions_s if c['probabilite'] >= 60]
        conditions_s_moderees = [c for c in conditions_s if 55 <= c['probabilite'] < 60]
        conditions_o_moderees = [c for c in conditions_o if c['probabilite'] >= 55]
        conditions_o_faibles = [c for c in conditions_o if 52 <= c['probabilite'] < 55]
        
        f.write(f"CONDITIONS S FORTES (≥60%): {len(conditions_s_fortes)}\n")
        f.write(f"CONDITIONS S MODÉRÉES (55-60%): {len(conditions_s_moderees)}\n")
        f.write(f"CONDITIONS O MODÉRÉES (≥55%): {len(conditions_o_moderees)}\n")
        f.write(f"CONDITIONS O FAIBLES (52-55%): {len(conditions_o_faibles)}\n\n")
        
        # Meilleure de chaque type
        if conditions_s_triees:
            meilleure_s = conditions_s_triees[0]
            f.write(f"MEILLEURE CONDITION S: {meilleure_s['nom']} ({meilleure_s['probabilite']:.1f}%)\n")
        
        if conditions_o_triees:
            meilleure_o = conditions_o_triees[0]
            f.write(f"MEILLEURE CONDITION O: {meilleure_o['nom']} ({meilleure_o['probabilite']:.1f}%)\n")
        
        # Couverture totale
        cas_s_total = sum(c['cas'] for c in conditions_s)
        cas_o_total = sum(c['cas'] for c in conditions_o)
        
        f.write(f"\nCOUVERTURE TOTALE:\n")
        f.write(f"Cas couverts S: {cas_s_total:,}\n")
        f.write(f"Cas couverts O: {cas_o_total:,}\n")
        f.write(f"Total cas: {cas_s_total + cas_o_total:,}\n")
    
    print(f"✅ Tableau prédictif final généré: {nom_fichier}")
    
    # Affichage résumé
    print(f"\n📊 RÉSUMÉ TABLEAU FINAL:")
    print(f"   Conditions S identifiées: {len(conditions_s)}")
    print(f"   Conditions O identifiées: {len(conditions_o)}")
    
    conditions_s_fortes = [c for c in conditions_s if c['probabilite'] >= 60]
    conditions_o_moderees = [c for c in conditions_o if c['probabilite'] >= 55]
    
    print(f"   Conditions S FORTES (≥60%): {len(conditions_s_fortes)}")
    print(f"   Conditions O MODÉRÉES (≥55%): {len(conditions_o_moderees)}")
    
    if conditions_s_triees:
        meilleure_s = conditions_s_triees[0]
        print(f"   Meilleure S: {meilleure_s['nom']} ({meilleure_s['probabilite']:.1f}%)")
    
    if conditions_o_triees:
        meilleure_o = conditions_o_triees[0]
        print(f"   Meilleure O: {meilleure_o['nom']} ({meilleure_o['probabilite']:.1f}%)")
    
    return nom_fichier

if __name__ == "__main__":
    print("🚀 GÉNÉRATION TABLEAU PRÉDICTIF FINAL S/O")
    print("=" * 70)
    
    # Génération du tableau final
    nom_fichier = generer_tableau_predictif_final()
    
    print(f"\n🎯 TABLEAU PRÉDICTIF FINAL CRÉÉ !")
    print("✅ Fusion complète des deux analyses")
    print("📊 Toutes les conditions S et O réunies")
    print("🚀 Système prédictif bidirectionnel complet")
    
    print("\n" + "=" * 70)
