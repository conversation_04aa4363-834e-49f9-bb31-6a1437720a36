#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test des optimisations avancées pour l'analyse sur 100,000 parties

Ce script vérifie que toutes les optimisations avancées sont bien intégrées
et fonctionnelles pour l'analyse du dataset complet.

Auteur: Expert Statisticien
Date: 2025-06-23
"""

import sys
import os
import time
from datetime import datetime

# Ajouter le répertoire courant au path pour les imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def tester_optimisations_avancees():
    """
    Test complet des optimisations avancées
    """
    print("🔬 TEST DES OPTIMISATIONS AVANCÉES")
    print("=" * 50)
    
    # Test 1: Vérification des bibliothèques d'optimisation
    print(f"\n📊 PHASE 1: VÉRIFICATION BIBLIOTHÈQUES")
    print("-" * 40)
    
    optimisations_disponibles = {}
    
    # Test orjson
    try:
        import orjson
        optimisations_disponibles['orjson'] = True
        print("✅ orjson disponible - parsing ultra-rapide")
        
        # Test de performance orjson
        test_data = {"test": list(range(10000))}
        start_time = time.time()
        serialized = orjson.dumps(test_data)
        parsed = orjson.loads(serialized)
        orjson_time = time.time() - start_time
        print(f"   Performance orjson: {orjson_time:.4f}s pour 10k éléments")
        
    except ImportError:
        optimisations_disponibles['orjson'] = False
        print("❌ orjson non disponible")
    
    # Test ijson
    try:
        import ijson
        optimisations_disponibles['ijson'] = True
        print("✅ ijson disponible - streaming")
    except ImportError:
        optimisations_disponibles['ijson'] = False
        print("❌ ijson non disponible")
    
    # Test numba
    try:
        from numba import jit
        import numpy as np
        optimisations_disponibles['numba'] = True
        print("✅ Numba disponible - JIT compilation")
        
        # Test de performance Numba
        @jit(nopython=True)
        def test_numba_function(arr):
            return np.sum(arr ** 2)
        
        test_array = np.random.random(100000)
        
        # Premier appel (compilation)
        start_time = time.time()
        result1 = test_numba_function(test_array)
        compile_time = time.time() - start_time
        
        # Deuxième appel (JIT compilé)
        start_time = time.time()
        result2 = test_numba_function(test_array)
        jit_time = time.time() - start_time
        
        print(f"   Performance Numba: {compile_time:.4f}s (compilation) → {jit_time:.6f}s (JIT)")
        
    except ImportError:
        optimisations_disponibles['numba'] = False
        print("❌ Numba non disponible")
    
    # Test multiprocessing
    try:
        import multiprocessing
        nb_cores = multiprocessing.cpu_count()
        optimisations_disponibles['multiprocessing'] = True
        print(f"✅ Multiprocessing disponible - {nb_cores} cœurs")
    except ImportError:
        optimisations_disponibles['multiprocessing'] = False
        print("❌ Multiprocessing non disponible")
    
    # Test 2: Vérification du module principal
    print(f"\n📊 PHASE 2: VÉRIFICATION MODULE PRINCIPAL")
    print("-" * 40)
    
    try:
        from analyseur_transitions_index5 import AnalyseurEvolutionEntropique, AnalyseurEvolutionRatios
        print("✅ Import du module principal réussi")
        
        # Vérifier que le dataset existe
        dataset_path = "dataset_baccarat_lupasco_20250622_011427.json"
        if os.path.exists(dataset_path):
            file_size_gb = os.path.getsize(dataset_path) / (1024 * 1024 * 1024)
            print(f"✅ Dataset trouvé: {file_size_gb:.2f}GB")
            
            # Test d'initialisation avec optimisations
            print("🔄 Test d'initialisation avec optimisations...")
            analyseur = AnalyseurEvolutionEntropique(dataset_path)
            print("✅ Analyseur initialisé avec optimisations")
            
        else:
            print(f"❌ Dataset non trouvé: {dataset_path}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur import module principal: {e}")
        return False
    
    # Test 3: Vérification du script d'analyse optimisé
    print(f"\n📊 PHASE 3: VÉRIFICATION SCRIPT ANALYSE OPTIMISÉ")
    print("-" * 40)
    
    try:
        from analyse_impact_ratios_patterns import analyser_patterns_batch_jit, analyser_differentiels_batch_jit
        print("✅ Import des fonctions JIT réussi")
        
        if optimisations_disponibles.get('numba', False):
            # Test des fonctions JIT
            test_ratios_l4 = np.random.random(1000)
            test_ratios_l5 = np.random.random(1000)
            test_patterns = np.random.randint(0, 2, 1000)  # 0=S, 1=O
            test_diff = np.random.random(1000)
            
            print("🔄 Test des fonctions JIT...")
            
            # Test fonction patterns
            start_time = time.time()
            resultats_patterns = analyser_patterns_batch_jit(test_ratios_l4, test_ratios_l5, test_patterns)
            patterns_time = time.time() - start_time
            print(f"   Fonction patterns JIT: {patterns_time:.6f}s pour 1000 éléments")
            
            # Test fonction différentiels
            start_time = time.time()
            resultats_diff = analyser_differentiels_batch_jit(test_diff, test_patterns)
            diff_time = time.time() - start_time
            print(f"   Fonction différentiels JIT: {diff_time:.6f}s pour 1000 éléments")
            
            print("✅ Fonctions JIT opérationnelles")
        else:
            print("⚠️ Numba non disponible - fonctions JIT désactivées")
            
    except Exception as e:
        print(f"❌ Erreur test fonctions JIT: {e}")
        return False
    
    # Test 4: Estimation des performances pour 100,000 parties
    print(f"\n📊 PHASE 4: ESTIMATION PERFORMANCES 100,000 PARTIES")
    print("-" * 50)
    
    # Calcul estimatif basé sur les optimisations disponibles
    facteur_acceleration = 1.0
    
    if optimisations_disponibles.get('orjson', False):
        facteur_acceleration *= 10  # orjson 10x plus rapide
        print("✅ Accélération orjson: 10x")
    
    if optimisations_disponibles.get('numba', False):
        facteur_acceleration *= 20  # Numba 20x plus rapide pour calculs
        print("✅ Accélération Numba: 20x")
    
    if optimisations_disponibles.get('multiprocessing', False):
        facteur_acceleration *= min(8, nb_cores)  # Parallélisation
        print(f"✅ Accélération multiprocessing: {min(8, nb_cores)}x")
    
    # Estimation temps sans optimisations (baseline)
    temps_baseline_heures = 24  # 24h pour 100k parties sans optimisations
    temps_optimise_heures = temps_baseline_heures / facteur_acceleration
    
    print(f"\n🎯 ESTIMATION TEMPS ANALYSE 100,000 PARTIES:")
    print(f"   Sans optimisations: {temps_baseline_heures:.1f}h")
    print(f"   Avec optimisations: {temps_optimise_heures:.1f}h")
    print(f"   Facteur d'accélération: {facteur_acceleration:.0f}x")
    
    if temps_optimise_heures < 4:
        print("🚀 EXCELLENT - Analyse réalisable en moins de 4h")
    elif temps_optimise_heures < 8:
        print("✅ BON - Analyse réalisable en moins de 8h")
    elif temps_optimise_heures < 24:
        print("⚠️ ACCEPTABLE - Analyse réalisable en moins de 24h")
    else:
        print("❌ LENT - Analyse > 24h, optimisations supplémentaires nécessaires")
    
    # Test 5: Recommandations d'optimisation
    print(f"\n📊 PHASE 5: RECOMMANDATIONS D'OPTIMISATION")
    print("-" * 45)
    
    score_optimisation = 0
    max_score = 4
    
    if optimisations_disponibles.get('orjson', False):
        score_optimisation += 1
    else:
        print("❌ INSTALLER orjson: pip install orjson")
    
    if optimisations_disponibles.get('numba', False):
        score_optimisation += 1
    else:
        print("❌ INSTALLER numba: pip install numba")
    
    if optimisations_disponibles.get('ijson', False):
        score_optimisation += 1
    else:
        print("❌ INSTALLER ijson: pip install ijson")
    
    if optimisations_disponibles.get('multiprocessing', False):
        score_optimisation += 1
    
    pourcentage_optimisation = (score_optimisation / max_score) * 100
    
    print(f"\n🏆 SCORE D'OPTIMISATION: {score_optimisation}/{max_score} ({pourcentage_optimisation:.0f}%)")
    
    if pourcentage_optimisation >= 100:
        print("🌟 PARFAIT - Toutes les optimisations sont activées")
    elif pourcentage_optimisation >= 75:
        print("✅ TRÈS BON - La plupart des optimisations sont activées")
    elif pourcentage_optimisation >= 50:
        print("⚠️ MOYEN - Certaines optimisations manquent")
    else:
        print("❌ INSUFFISANT - Optimisations majeures manquantes")
    
    return pourcentage_optimisation >= 75


if __name__ == "__main__":
    print("🚀 LANCEMENT TEST OPTIMISATIONS AVANCÉES")
    print("=" * 60)
    
    # Test principal
    success = tester_optimisations_avancees()
    
    if success:
        print(f"\n🎯 OPTIMISATIONS VALIDÉES !")
        print("✅ Le système est prêt pour l'analyse sur 100,000 parties")
        print("🚀 Performance optimale garantie")
    else:
        print(f"\n❌ OPTIMISATIONS INSUFFISANTES")
        print("⚠️ Installer les bibliothèques manquantes avant l'analyse")
        print("📊 L'analyse sera plus lente sans optimisations")
    
    print("\n" + "=" * 60)
