#!/usr/bin/env python3
"""
LANCEMENT DU DÉTECTEUR DE CHAOS ORGANISÉ - VERSION CORRIGÉE
===========================================================

Script pour lancer l'analyse entropique complète avec les corrections :
1. Chargement du VRAI dataset (100,000 parties)
2. Fenêtres glissantes DOUBLES (longueur 4 et 5)
3. Calculs depuis main 5 et main 6
4. Optimisations ultra-rapides intégrées
"""

import os
import time
from datetime import datetime


def verifier_dataset():
    """Vérifie que le vrai dataset est disponible"""
    
    dataset_path = "dataset_baccarat_lupasco_20250622_011427.json"
    
    if not os.path.exists(dataset_path):
        print(f"❌ ERREUR : Dataset introuvable : {dataset_path}")
        print("📁 Fichiers disponibles :")
        for file in os.listdir("."):
            if file.endswith(".json"):
                size_mb = os.path.getsize(file) / (1024**2)
                print(f"   {file} ({size_mb:.1f} MB)")
        return False
    
    # Vérifier la taille du fichier
    size_gb = os.path.getsize(dataset_path) / (1024**3)
    print(f"✅ Dataset trouvé : {dataset_path}")
    print(f"📊 Taille : {size_gb:.2f} GB")
    
    if size_gb < 5:
        print("⚠️ ATTENTION : Fichier plus petit qu'attendu pour 100,000 parties")
    
    return True


def lancer_detecteur_chaos():
    """Lance le détecteur de chaos organisé avec les corrections"""
    
    print("🚀 LANCEMENT DU DÉTECTEUR DE CHAOS ORGANISÉ - VERSION CORRIGÉE")
    print("=" * 70)
    
    # 1. Vérifier le dataset
    if not verifier_dataset():
        return False
    
    # 2. Importer l'analyseur corrigé
    try:
        from analyseur_transitions_index5 import AnalyseurEntropiqueIntegre
        print("✅ Analyseur entropique importé avec succès")
    except Exception as e:
        print(f"❌ Erreur import analyseur : {e}")
        return False
    
    # 3. Créer l'analyseur avec le vrai dataset
    dataset_path = "dataset_baccarat_lupasco_20250622_011427.json"
    
    try:
        print(f"\n🎯 Initialisation de l'analyseur...")
        analyseur = AnalyseurEntropiqueIntegre(dataset_path)
        print("✅ Analyseur initialisé")
    except Exception as e:
        print(f"❌ Erreur initialisation : {e}")
        return False
    
    # 4. Lancer l'analyse complète
    print(f"\n🔥 LANCEMENT DE L'ANALYSE COMPLÈTE...")
    print("⚡ Fenêtres glissantes doubles : Longueur 4 (main 5+) et Longueur 5 (main 6+)")
    print("🎯 Objectif : Détecter les singularités de chaos dans 100,000 parties")
    
    start_time = time.time()
    
    try:
        # Analyser un échantillon d'abord pour tester
        print(f"\n🧪 TEST PRÉLIMINAIRE : Analyse de 10 parties...")
        resultats_test = analyseur.analyser_dataset_complet(nb_parties_max=10)
        
        if 'erreur' in resultats_test:
            print(f"❌ Erreur test : {resultats_test['erreur']}")
            return False
        
        # Afficher les résultats du test
        stats_test = resultats_test['statistiques_globales']
        print(f"\n✅ TEST RÉUSSI !")
        print(f"📊 Parties analysées : {resultats_test['nb_parties_analysees']}")
        print(f"🎯 Prédictions générées : {stats_test['total_predictions_globales']}")
        print(f"✅ Taux de succès : {stats_test['taux_succes_global']:.2f}%")
        
        # LANCEMENT DIRECT DE L'ANALYSE COMPLÈTE
        print(f"\n🔥 LANCEMENT AUTOMATIQUE DE L'ANALYSE COMPLÈTE...")
        print("⚠️ Temps estimé : 2-3 heures")
        print("💾 RAM utilisée : ~20-25 GB")

        resultats_complets = analyseur.analyser_dataset_complet()  # Toutes les parties

        end_time = time.time()
        duree_minutes = (end_time - start_time) / 60

        if 'erreur' not in resultats_complets:
            print(f"\n🎉 ANALYSE COMPLÈTE TERMINÉE !")
            print(f"⏱️ Durée totale : {duree_minutes:.1f} minutes")

            # Afficher les résultats
            stats_finales = resultats_complets['statistiques_globales']
            print(f"\n📊 RÉSULTATS FINAUX :")
            print(f"   Parties analysées : {resultats_complets['nb_parties_analysees']:,}")
            print(f"   Prédictions totales : {stats_finales['total_predictions_globales']:,}")
            print(f"   Taux de succès global : {stats_finales['taux_succes_global']:.2f}%")

            # Export des résultats
            print(f"\n💾 Export des résultats...")
            fichier_json = analyseur.exporter_resultats_complets(resultats_complets, 'json')
            fichier_txt = analyseur.exporter_resultats_complets(resultats_complets, 'txt')

            print(f"✅ Fichiers exportés :")
            print(f"   JSON : {fichier_json}")
            print(f"   TXT  : {fichier_txt}")

            return True
        else:
            print(f"❌ Erreur analyse complète : {resultats_complets['erreur']}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur inattendue : {e}")
        import traceback
        traceback.print_exc()
        return False


def afficher_instructions():
    """Affiche les instructions pour interpréter les résultats"""
    
    print(f"\n📋 INSTRUCTIONS POUR INTERPRÉTER LES RÉSULTATS")
    print("=" * 50)
    print("🔍 Rechercher dans le rapport TXT :")
    print("   • 'PERFORMANCE PAR ZONE DE RATIO'")
    print("   • Ratios 0.0-0.5 : Zone de singularité de chaos")
    print("   • Taux de succès > 55% = Singularité détectée")
    print("")
    print("🎯 Indicateurs clés :")
    print("   • Ratio < 0.5 = Ordre local extrême → Correction vers chaos")
    print("   • Taux succès élevé = Prédictibilité confirmée")
    print("   • Fréquence élevée = Opportunités exploitables")
    print("")
    print("🚀 Si singularités détectées :")
    print("   • Système non-aléatoire confirmé")
    print("   • Mécanisme d'auto-régulation validé")
    print("   • Stratégie révolutionnaire possible")


if __name__ == "__main__":
    print("🎯 DÉTECTEUR DE CHAOS ORGANISÉ - VERSION CORRIGÉE")
    print("=" * 60)
    print("🔧 Corrections appliquées :")
    print("   ✅ Chargement du VRAI dataset (100,000 parties)")
    print("   ✅ Fenêtres glissantes DOUBLES (longueur 4 et 5)")
    print("   ✅ Calculs depuis main 5 et main 6")
    print("   ✅ Optimisations ultra-rapides")
    print("")
    
    # Lancer le détecteur
    succes = lancer_detecteur_chaos()
    
    if succes:
        afficher_instructions()
        print(f"\n🎉 MISSION ACCOMPLIE !")
        print("🔬 Les singularités de chaos ont été analysées")
    else:
        print(f"\n❌ MISSION ÉCHOUÉE")
        print("🔧 Vérifier les corrections et relancer")
