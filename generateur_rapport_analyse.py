#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GENERATEUR RAPPORT ANALYSE
=========================

Classe spécialisée pour la génération des rapports d'analyse du baccarat.
Produit des rapports détaillés avec visualisations et exports de données.

Auteur: Expert Statisticien
Date: 2025-06-26
Version: 1.0

Fonctionnalités:
- Rapport principal avec métriques significatives
- Analyses statistiques détaillées
- Export des données pour analyse ultérieure
- Visualisations des distributions
"""

import os
import json
import datetime
from typing import Dict, List, Tuple, Optional, Any
import numpy as np


class GenerateurRapportAnalyse:
    """
    Classe principale pour la génération des rapports d'analyse.
    
    Responsabilités:
    - Génération du rapport principal
    - Export des données significatives
    - Création des visualisations
    - Sauvegarde des résultats
    """
    
    def __init__(self, config):
        self.config = config
        self.timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        self.nom_rapport = f"rapport_analyse_baccarat_{self.timestamp}.txt"
        self.nom_export_json = f"donnees_significatives_{self.timestamp}.json"
        
    def generer_rapport(self, donnees_analysees: List, resultats_significance: Dict, 
                       stats_performance: Dict) -> bool:
        """
        Génère le rapport complet d'analyse.
        
        Args:
            donnees_analysees: Données analysées main par main
            resultats_significance: Résultats de l'analyse de significance
            stats_performance: Statistiques de performance
            
        Returns:
            bool: True si la génération réussit, False sinon
        """
        try:
            print(f"Génération du rapport: {self.nom_rapport}")
            
            with open(self.nom_rapport, 'w', encoding='utf-8') as f:
                # En-tête du rapport
                self._ecrire_entete_rapport(f)
                
                # Résumé exécutif
                self._ecrire_resume_executif(f, donnees_analysees, resultats_significance, stats_performance)
                
                # Analyse des métriques significatives
                self._ecrire_analyse_metriques_significatives(f, resultats_significance)
                
                # Distributions des patterns
                self._ecrire_analyse_distributions(f, donnees_analysees)
                
                # Analyse de la variable DIFF
                self._ecrire_analyse_variable_diff(f, donnees_analysees)
                
                # Recommandations
                self._ecrire_recommandations(f, resultats_significance)
                
                # Annexes techniques
                self._ecrire_annexes_techniques(f, stats_performance)
            
            # Export des données significatives
            self._exporter_donnees_significatives(donnees_analysees, resultats_significance)
            
            print(f"Rapport généré avec succès: {self.nom_rapport}")
            return True
            
        except Exception as e:
            print(f"ERREUR lors de la génération du rapport: {e}")
            return False
    
    def _ecrire_entete_rapport(self, f):
        """Écrit l'en-tête du rapport."""
        f.write("=" * 80 + "\n")
        f.write("RAPPORT D'ANALYSE BACCARAT OPTIMISE\n")
        f.write("=" * 80 + "\n\n")
        f.write(f"Date de génération: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Version analyseur: 1.0\n")
        f.write(f"Dataset: {self.config.dataset_path}\n")
        f.write(f"Parties analysées (max): {self.config.nb_parties_max}\n")
        f.write(f"Patterns analysés: {', '.join(self.config.patterns_analyses)}\n")
        f.write("\n" + "=" * 80 + "\n\n")
    
    def _ecrire_resume_executif(self, f, donnees_analysees: List, resultats_significance: Dict, 
                               stats_performance: Dict):
        """Écrit le résumé exécutif."""
        f.write("RÉSUMÉ EXÉCUTIF\n")
        f.write("-" * 40 + "\n\n")
        
        # Statistiques générales
        total_mains = len(donnees_analysees)
        donnees_so = [d for d in donnees_analysees if d.pattern in ['S', 'O']]
        total_so = len(donnees_so)
        
        f.write(f"Total mains analysées: {total_mains:,}\n")
        f.write(f"Mains S/O analysées: {total_so:,} ({total_so/total_mains*100:.1f}%)\n")
        f.write(f"Parties traitées: {stats_performance.get('nb_parties_traitees', 0):,}\n")
        f.write(f"Temps total d'analyse: {stats_performance.get('temps_calcul', 0):.2f}s\n\n")
        
        # Distribution des patterns
        patterns_count = {}
        for donnee in donnees_so:
            pattern = donnee.pattern
            patterns_count[pattern] = patterns_count.get(pattern, 0) + 1
        
        f.write("Distribution des patterns S/O:\n")
        for pattern in ['S', 'O']:
            if pattern in patterns_count:
                count = patterns_count[pattern]
                pourcentage = (count / total_so) * 100
                f.write(f"  Pattern {pattern}: {count:,} ({pourcentage:.1f}%)\n")
        
        # Métriques significatives
        if resultats_significance:
            significatives = [r for r in resultats_significance.values() if r.significatif]
            total_metriques = len(resultats_significance)
            
            f.write(f"\nMétriques analysées: {total_metriques}\n")
            f.write(f"Métriques significatives: {len(significatives)} ({len(significatives)/total_metriques*100:.1f}%)\n")
        
        f.write("\n" + "=" * 80 + "\n\n")
    
    def _ecrire_analyse_metriques_significatives(self, f, resultats_significance: Dict):
        """Écrit l'analyse des métriques significatives."""
        f.write("ANALYSE DES MÉTRIQUES SIGNIFICATIVES\n")
        f.write("-" * 40 + "\n\n")
        
        if not resultats_significance:
            f.write("Aucune analyse de significance disponible.\n\n")
            return
        
        # Métriques significatives triées par importance
        significatives = [(nom, res) for nom, res in resultats_significance.items() if res.significatif]
        significatives.sort(key=lambda x: abs(x[1].effet_size), reverse=True)
        
        if not significatives:
            f.write("Aucune métrique significative identifiée.\n\n")
            return
        
        f.write(f"Nombre de métriques significatives: {len(significatives)}\n")
        f.write("Classement par taille d'effet (Cohen's d):\n\n")
        
        for i, (nom_metrique, resultat) in enumerate(significatives[:20], 1):  # Top 20
            f.write(f"{i:2d}. {nom_metrique}\n")
            f.write(f"    Test: {resultat.test_statistique}\n")
            f.write(f"    P-value: {resultat.p_value:.6f}\n")
            f.write(f"    Effet size: {resultat.effet_size:.4f}\n")
            f.write(f"    Moyenne S: {resultat.moyennes_par_pattern['S']:.4f}\n")
            f.write(f"    Moyenne O: {resultat.moyennes_par_pattern['O']:.4f}\n")
            f.write(f"    Interprétation: {resultat.interpretation}\n\n")
        
        # Résumé par catégorie d'effet
        f.write("Résumé par taille d'effet:\n")
        categories = {'Grand (≥0.8)': 0, 'Moyen (0.5-0.8)': 0, 'Petit (0.2-0.5)': 0}
        
        for _, resultat in significatives:
            effet = abs(resultat.effet_size)
            if effet >= 0.8:
                categories['Grand (≥0.8)'] += 1
            elif effet >= 0.5:
                categories['Moyen (0.5-0.8)'] += 1
            else:
                categories['Petit (0.2-0.5)'] += 1
        
        for categorie, count in categories.items():
            f.write(f"  {categorie}: {count} métriques\n")
        
        f.write("\n" + "=" * 80 + "\n\n")
    
    def _ecrire_analyse_distributions(self, f, donnees_analysees: List):
        """Écrit l'analyse des distributions."""
        f.write("ANALYSE DES DISTRIBUTIONS\n")
        f.write("-" * 40 + "\n\n")
        
        donnees_so = [d for d in donnees_analysees if d.pattern in ['S', 'O']]
        
        if not donnees_so:
            f.write("Aucune donnée S/O disponible.\n\n")
            return
        
        # Analyse des métriques de base
        metriques_base = ['ratio_l4', 'ratio_l5', 'diff', 'entropie_globale']
        
        for metrique in metriques_base:
            f.write(f"Distribution de {metrique}:\n")
            
            # Extraction des valeurs par pattern
            valeurs_s = []
            valeurs_o = []
            
            for donnee in donnees_so:
                if metrique == 'ratio_l4':
                    val = donnee.ratio_l4
                elif metrique == 'ratio_l5':
                    val = donnee.ratio_l5
                elif metrique == 'diff':
                    val = donnee.diff
                elif metrique == 'entropie_globale':
                    val = donnee.entropie_globale
                else:
                    continue
                
                if donnee.pattern == 'S':
                    valeurs_s.append(val)
                else:
                    valeurs_o.append(val)
            
            # Statistiques descriptives
            if valeurs_s:
                f.write(f"  Pattern S (n={len(valeurs_s)}):\n")
                f.write(f"    Moyenne: {np.mean(valeurs_s):.4f}\n")
                f.write(f"    Médiane: {np.median(valeurs_s):.4f}\n")
                f.write(f"    Écart-type: {np.std(valeurs_s):.4f}\n")
                f.write(f"    Min-Max: {np.min(valeurs_s):.4f} - {np.max(valeurs_s):.4f}\n")
            
            if valeurs_o:
                f.write(f"  Pattern O (n={len(valeurs_o)}):\n")
                f.write(f"    Moyenne: {np.mean(valeurs_o):.4f}\n")
                f.write(f"    Médiane: {np.median(valeurs_o):.4f}\n")
                f.write(f"    Écart-type: {np.std(valeurs_o):.4f}\n")
                f.write(f"    Min-Max: {np.min(valeurs_o):.4f} - {np.max(valeurs_o):.4f}\n")
            
            f.write("\n")
        
        f.write("=" * 80 + "\n\n")
    
    def _ecrire_analyse_variable_diff(self, f, donnees_analysees: List):
        """Écrit l'analyse spécialisée de la variable DIFF."""
        f.write("ANALYSE DE LA VARIABLE DIFF\n")
        f.write("-" * 40 + "\n\n")
        
        donnees_so = [d for d in donnees_analysees if d.pattern in ['S', 'O']]
        
        if not donnees_so:
            f.write("Aucune donnée S/O disponible.\n\n")
            return
        
        # Analyse par tranches DIFF
        f.write("Distribution des patterns par tranches DIFF:\n\n")
        
        for seuil in self.config.seuils_diff_analyse:
            donnees_tranche = [d for d in donnees_so if d.diff < seuil]
            
            if len(donnees_tranche) < 10:
                continue
            
            count_s = len([d for d in donnees_tranche if d.pattern == 'S'])
            count_o = len([d for d in donnees_tranche if d.pattern == 'O'])
            total_tranche = len(donnees_tranche)
            
            pct_s = (count_s / total_tranche) * 100
            pct_o = (count_o / total_tranche) * 100
            
            f.write(f"DIFF < {seuil:.3f} (n={total_tranche}):\n")
            f.write(f"  Pattern S: {count_s} ({pct_s:.1f}%)\n")
            f.write(f"  Pattern O: {count_o} ({pct_o:.1f}%)\n")
            f.write(f"  Ratio S/O: {pct_s/pct_o:.2f}\n\n")
        
        # Statistiques globales DIFF
        diff_values = [d.diff for d in donnees_so]
        f.write("Statistiques globales DIFF:\n")
        f.write(f"  Moyenne: {np.mean(diff_values):.4f}\n")
        f.write(f"  Médiane: {np.median(diff_values):.4f}\n")
        f.write(f"  Écart-type: {np.std(diff_values):.4f}\n")
        f.write(f"  Percentiles: P25={np.percentile(diff_values, 25):.4f}, ")
        f.write(f"P75={np.percentile(diff_values, 75):.4f}\n")
        f.write(f"  Min-Max: {np.min(diff_values):.4f} - {np.max(diff_values):.4f}\n\n")
        
        f.write("=" * 80 + "\n\n")
    
    def _ecrire_recommandations(self, f, resultats_significance: Dict):
        """Écrit les recommandations basées sur l'analyse."""
        f.write("RECOMMANDATIONS\n")
        f.write("-" * 40 + "\n\n")
        
        if not resultats_significance:
            f.write("Aucune recommandation disponible sans analyse de significance.\n\n")
            return
        
        significatives = [r for r in resultats_significance.values() if r.significatif]
        
        if not significatives:
            f.write("RECOMMANDATION PRINCIPALE:\n")
            f.write("Aucune métrique significative identifiée. Les patterns S/O semblent\n")
            f.write("suivre une distribution aléatoire sans biais exploitable détectable.\n\n")
            f.write("Actions suggérées:\n")
            f.write("1. Augmenter la taille de l'échantillon\n")
            f.write("2. Affiner les métriques d'entropie\n")
            f.write("3. Explorer d'autres fenêtres temporelles (L3, L6, etc.)\n\n")
        else:
            f.write("RECOMMANDATIONS BASÉES SUR LES MÉTRIQUES SIGNIFICATIVES:\n\n")
            
            # Top 5 métriques
            top_metriques = sorted(significatives, key=lambda x: abs(x.effet_size), reverse=True)[:5]
            
            f.write("1. MÉTRIQUES PRIORITAIRES À SURVEILLER:\n")
            for i, metrique in enumerate(top_metriques, 1):
                f.write(f"   {i}. {metrique.nom_metrique} (effet size: {metrique.effet_size:.3f})\n")
            
            f.write("\n2. STRATÉGIE D'ANALYSE RECOMMANDÉE:\n")
            f.write("   - Concentrer l'analyse sur les métriques à fort effet size\n")
            f.write("   - Valider les résultats sur un échantillon indépendant\n")
            f.write("   - Implémenter un système de monitoring en temps réel\n\n")
            
            f.write("3. DÉVELOPPEMENTS FUTURS:\n")
            f.write("   - Intégrer les métriques significatives dans un modèle prédictif\n")
            f.write("   - Développer des seuils adaptatifs basés sur les distributions\n")
            f.write("   - Explorer les interactions entre métriques significatives\n\n")
        
        f.write("=" * 80 + "\n\n")
    
    def _ecrire_annexes_techniques(self, f, stats_performance: Dict):
        """Écrit les annexes techniques."""
        f.write("ANNEXES TECHNIQUES\n")
        f.write("-" * 40 + "\n\n")
        
        f.write("A. PARAMÈTRES D'ANALYSE:\n")
        f.write(f"   - Seuil p-value: 0.05\n")
        f.write(f"   - Seuil effet size minimum: 0.2\n")
        f.write(f"   - Test statistique principal: Mann-Whitney U\n")
        f.write(f"   - Patterns analysés: {', '.join(self.config.patterns_analyses)}\n\n")
        
        f.write("B. PERFORMANCE:\n")
        f.write(f"   - Temps chargement: {stats_performance.get('temps_chargement', 0):.2f}s\n")
        f.write(f"   - Temps calcul: {stats_performance.get('temps_calcul', 0):.2f}s\n")
        f.write(f"   - Mémoire utilisée: {stats_performance.get('memoire_utilisee', 0)} MB\n\n")
        
        f.write("C. MÉTHODOLOGIE:\n")
        f.write("   - Approche analytique pure (pas de seuils prédictifs arbitraires)\n")
        f.write("   - Tests non-paramétriques robustes aux outliers\n")
        f.write("   - Analyse main par main avec préservation du contexte temporel\n")
        f.write("   - Focus sur patterns S/O uniquement\n\n")
        
        f.write("D. LIMITATIONS:\n")
        f.write("   - Corrélations désactivées pour optimisation performance\n")
        f.write("   - Analyse limitée aux patterns S/O (TIE exclus de l'analyse finale)\n")
        f.write("   - Métriques avancées en développement progressif\n\n")
        
        f.write("=" * 80 + "\n")
        f.write("FIN DU RAPPORT\n")
        f.write("=" * 80 + "\n")
    
    def _exporter_donnees_significatives(self, donnees_analysees: List, resultats_significance: Dict):
        """Exporte les données significatives au format JSON."""
        try:
            # Préparation des données d'export
            export_data = {
                'metadata': {
                    'timestamp': self.timestamp,
                    'total_mains': len(donnees_analysees),
                    'dataset': self.config.dataset_path,
                    'patterns_analyses': self.config.patterns_analyses
                },
                'metriques_significatives': {},
                'resume_statistique': {}
            }
            
            # Export des métriques significatives
            if resultats_significance:
                significatives = {nom: {
                    'p_value': res.p_value,
                    'effet_size': res.effet_size,
                    'moyennes_par_pattern': res.moyennes_par_pattern,
                    'interpretation': res.interpretation
                } for nom, res in resultats_significance.items() if res.significatif}
                
                export_data['metriques_significatives'] = significatives
            
            # Statistiques de base
            donnees_so = [d for d in donnees_analysees if d.pattern in ['S', 'O']]
            if donnees_so:
                patterns_count = {}
                for donnee in donnees_so:
                    patterns_count[donnee.pattern] = patterns_count.get(donnee.pattern, 0) + 1
                
                export_data['resume_statistique'] = {
                    'total_so': len(donnees_so),
                    'distribution_patterns': patterns_count,
                    'variable_diff': {
                        'moyenne': float(np.mean([d.diff for d in donnees_so])),
                        'mediane': float(np.median([d.diff for d in donnees_so])),
                        'ecart_type': float(np.std([d.diff for d in donnees_so]))
                    }
                }
            
            # Sauvegarde
            with open(self.nom_export_json, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)
            
            print(f"Données exportées: {self.nom_export_json}")
            
        except Exception as e:
            print(f"ERREUR lors de l'export JSON: {e}")
