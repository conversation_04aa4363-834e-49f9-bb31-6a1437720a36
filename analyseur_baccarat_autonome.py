#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ANALYSEUR BACCARAT AUTONOME
==========================

Analyseur complet et autonome pour l'analyse des parties de baccarat.
Basé sur analyse_complete_avec_diff.py mais en fichier unique sans dépendances externes.

CARACTÉRISTIQUES :
- Chargement JSON direct (sans analyseur_transitions_index5.py)
- Calculs entropiques intégrés (L4/L5)
- Calculs ratios intégrés (L4/L5)
- Variable DIFF = |L4-L5| pour qualité du signal
- Corrélations essentielles uniquement (6 au lieu de 1,326+)
- Analyse conditions prédictives S/O
- Génération de rapports optimisée

PHASES D'ANALYSE :
1. Chargement des données JSON
2. Calculs entropiques par main
3. Calculs ratios L4/L5 par main
4. Calcul variable DIFF
5. Analyse conditions prédictives
6. Corrélations essentielles
7. Génération rapport final

Auteur: Expert Statisticien
Date: 2025-06-26
Version: Autonome v1.0
"""

# ============================================================================
# I. IMPORTS ET CONFIGURATION
# ============================================================================

import json
import math
import os
import sys
from datetime import datetime
from collections import Counter, defaultdict
from typing import Dict, List, Tuple, Any, Optional

# Configuration globale
DATASET_PATH = "dataset_baccarat_lupasco_20250624_104837.json"
NB_PARTIES_MAX = 100000
SEUIL_MIN_DONNEES = 100

# Seuils DIFF pour qualité du signal
SEUILS_DIFF = {
    'PARFAIT': 0.020,      # Signal parfait (confiance 95%)
    'EXCELLENT': 0.030,    # Signal excellent (confiance 90%)
    'TRES_BON': 0.050,     # Signal très bon (confiance 85%)
    'DOUTEUX': 0.150       # Signal douteux (abstention)
}

# ============================================================================
# II. CLASSE PRINCIPALE - ANALYSEUR BACCARAT AUTONOME
# ============================================================================

class AnalyseurBaccaratAutonome:
    """
    ANALYSEUR BACCARAT AUTONOME
    ==========================
    
    Classe principale qui orchestre toute l'analyse de baccarat en un seul fichier.
    Intègre tous les calculs nécessaires sans dépendances externes.
    """
    
    def __init__(self, dataset_path: str = DATASET_PATH):
        """
        Initialise l'analyseur autonome.
        
        Args:
            dataset_path (str): Chemin vers le fichier JSON des données
        """
        self.dataset_path = dataset_path
        self.donnees_brutes = []
        self.donnees_analyse = []
        self.conditions_s = []
        self.conditions_o = []
        self.correlations_essentielles = {}
        
        print("🚀 ANALYSEUR BACCARAT AUTONOME INITIALISÉ")
        print(f"📁 Dataset: {dataset_path}")
        print("=" * 60)
    
    def analyser_complet(self) -> bool:
        """
        Lance l'analyse complète en 7 phases.
        
        Returns:
            bool: True si succès, False sinon
        """
        try:
            print("🔬 DÉBUT ANALYSE COMPLÈTE AUTONOME")
            print("=" * 60)
            
            # PHASE 1: Chargement des données JSON
            if not self._phase1_charger_donnees():
                return False
            
            # PHASE 2: Calculs entropiques par main
            if not self._phase2_calculs_entropiques():
                return False
            
            # PHASE 3: Calculs ratios L4/L5 par main
            if not self._phase3_calculs_ratios():
                return False
            
            # PHASE 4: Calcul variable DIFF et extraction données
            if not self._phase4_calcul_diff():
                return False
            
            # PHASE 5: Analyse conditions prédictives
            if not self._phase5_analyse_conditions():
                return False
            
            # PHASE 6: Corrélations essentielles
            if not self._phase6_correlations_essentielles():
                return False
            
            # PHASE 7: Génération rapport final
            if not self._phase7_generer_rapport():
                return False
            
            print("\n🎯 ANALYSE COMPLÈTE RÉUSSIE !")
            print("✅ Toutes les phases terminées avec succès")
            print("=" * 60)
            
            return True
            
        except Exception as e:
            print(f"❌ ERREUR ANALYSE COMPLÈTE: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    # ========================================================================
    # PHASE 1: CHARGEMENT DES DONNÉES JSON
    # ========================================================================

    def _phase1_charger_donnees(self) -> bool:
        """
        PHASE 1: Charge les données JSON directement avec optimisations.

        Returns:
            bool: True si succès, False sinon
        """
        print("\n📊 PHASE 1: CHARGEMENT DONNÉES JSON")
        print("-" * 50)

        try:
            if not os.path.exists(self.dataset_path):
                print(f"❌ Fichier non trouvé: {self.dataset_path}")
                return False

            # Tenter chargement optimisé avec orjson, fallback sur JSON standard
            self.donnees_brutes = self._charger_json_optimise()

            if not self.donnees_brutes:
                print("❌ Aucune donnée chargée")
                return False

            # Extraire les parties du dataset
            if isinstance(self.donnees_brutes, dict) and 'parties' in self.donnees_brutes:
                parties = self.donnees_brutes['parties']
            elif isinstance(self.donnees_brutes, list):
                parties = self.donnees_brutes
            else:
                print("❌ Format de données non reconnu")
                return False

            nb_parties = len(parties)
            print(f"✅ {nb_parties:,} parties chargées")

            # Limiter le nombre de parties si nécessaire
            if nb_parties > NB_PARTIES_MAX:
                parties = parties[:NB_PARTIES_MAX]
                print(f"⚠️  Limitation à {NB_PARTIES_MAX:,} parties")

            # Stocker les parties optimisées
            self.donnees_brutes = parties

            # Validation des données
            if not self._valider_structure_donnees():
                return False

            return True

        except Exception as e:
            print(f"❌ Erreur chargement JSON: {e}")
            import traceback
            traceback.print_exc()
            return False

    def _charger_json_optimise(self):
        """
        Charge le JSON avec la méthode la plus rapide disponible.
        Copié et adapté de analyseur_transitions_index5.py
        """
        # Tenter orjson d'abord (plus rapide)
        try:
            import orjson
            print("⚡ Chargement avec orjson (ultra-rapide)...")

            with open(self.dataset_path, 'rb') as f:
                data_bytes = f.read()
                data = orjson.loads(data_bytes)
                print("✅ Parsing orjson terminé")
                return data

        except ImportError:
            print("⚠️  orjson non disponible, utilisation JSON standard")
        except Exception as e:
            print(f"⚠️  Erreur orjson: {e}, fallback JSON standard")

        # Fallback sur JSON standard
        try:
            print("🔄 Chargement avec JSON standard...")

            with open(self.dataset_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                print("✅ Parsing JSON standard terminé")
                return data

        except Exception as e:
            print(f"❌ Erreur JSON standard: {e}")
            raise

    def _valider_structure_donnees(self) -> bool:
        """
        Valide la structure des données chargées.
        """
        if not self.donnees_brutes:
            print("❌ Aucune donnée à valider")
            return False

        # Vérifier quelques parties pour s'assurer de la structure
        parties_a_verifier = min(5, len(self.donnees_brutes))

        for i in range(parties_a_verifier):
            partie = self.donnees_brutes[i]

            if not isinstance(partie, dict):
                print(f"❌ Partie {i} n'est pas un dictionnaire")
                return False

            if 'mains' not in partie:
                print(f"❌ Partie {i} n'a pas de clé 'mains'")
                return False

            mains = partie['mains']
            if not isinstance(mains, list) or len(mains) == 0:
                print(f"❌ Partie {i} n'a pas de mains valides")
                return False

            # Vérifier la première main
            main = mains[0]
            if not isinstance(main, dict):
                print(f"❌ Main 0 de partie {i} n'est pas un dictionnaire")
                return False

            # Vérifier les champs essentiels
            champs_requis = ['main_number', 'index5_combined', 'index3_result']
            for champ in champs_requis:
                if champ not in main:
                    print(f"❌ Champ '{champ}' manquant dans main 0 de partie {i}")
                    return False

        print(f"✅ Structure des données validée ({parties_a_verifier} parties vérifiées)")
        return True
    
    # ========================================================================
    # PHASE 2: CALCULS ENTROPIQUES L4/L5
    # ========================================================================

    def _phase2_calculs_entropiques(self) -> bool:
        """
        PHASE 2: Calcule les entropies L4/L5 pour chaque main.
        Copié et adapté de analyseur_transitions_index5.py

        Returns:
            bool: True si succès, False sinon
        """
        print("\n📊 PHASE 2: CALCULS ENTROPIQUES L4/L5")
        print("-" * 50)

        try:
            # Initialiser les structures de données pour les entropies
            self.entropies_l4 = {}  # {partie_id: {main_number: entropie_l4}}
            self.entropies_l5 = {}  # {partie_id: {main_number: entropie_l5}}
            self.patterns_soe = {}  # {partie_id: [patterns S/O/E par main]}

            nb_parties_traitees = 0
            nb_mains_traitees = 0

            print("🔄 Calcul des entropies L4/L5 pour toutes les parties...")

            for partie in self.donnees_brutes:
                partie_id = partie.get('partie_number', nb_parties_traitees)
                mains = partie.get('mains', [])

                if len(mains) < 5:  # Besoin d'au moins 5 mains pour L4/L5
                    continue

                # Extraire les résultats INDEX3 pour les patterns S/O/E
                index3_resultats = [main.get('index3_result', '') for main in mains]

                # Calculer patterns S/O/E
                patterns = self._calculer_patterns_soe(index3_resultats)
                self.patterns_soe[partie_id] = patterns

                # Calculer entropies L4 et L5 pour chaque main (à partir de la main 4 pour L4, main 5 pour L5)
                entropies_l4_partie = {}
                entropies_l5_partie = {}

                for i in range(len(mains)):
                    main_number = mains[i].get('main_number', i)

                    # Entropie L4 (fenêtre de 4 mains précédentes)
                    if i >= 4:  # Besoin de 4 mains précédentes
                        fenetre_l4 = index3_resultats[i-4:i]
                        entropie_l4 = self._calculer_entropie_locale(fenetre_l4)
                        entropies_l4_partie[main_number] = entropie_l4

                    # Entropie L5 (fenêtre de 5 mains précédentes)
                    if i >= 5:  # Besoin de 5 mains précédentes
                        fenetre_l5 = index3_resultats[i-5:i]
                        entropie_l5 = self._calculer_entropie_locale(fenetre_l5)
                        entropies_l5_partie[main_number] = entropie_l5

                    nb_mains_traitees += 1

                self.entropies_l4[partie_id] = entropies_l4_partie
                self.entropies_l5[partie_id] = entropies_l5_partie

                nb_parties_traitees += 1

                # Affichage progression
                if nb_parties_traitees % 1000 == 0:
                    print(f"   📈 {nb_parties_traitees:,} parties traitées...")

            print(f"✅ Entropies calculées: {nb_parties_traitees:,} parties, {nb_mains_traitees:,} mains")
            print(f"   - Entropies L4: {sum(len(e) for e in self.entropies_l4.values()):,} valeurs")
            print(f"   - Entropies L5: {sum(len(e) for e in self.entropies_l5.values()):,} valeurs")
            print(f"   - Patterns S/O/E: {sum(len(p) for p in self.patterns_soe.values()):,} patterns")

            return True

        except Exception as e:
            print(f"❌ Erreur calculs entropiques: {e}")
            import traceback
            traceback.print_exc()
            return False

    def _calculer_entropie_locale(self, fenetre_resultats: List[str]) -> float:
        """
        Calcule l'entropie locale d'une fenêtre de résultats INDEX3.
        Copié et adapté de analyseur_transitions_index5.py

        Args:
            fenetre_resultats: Liste des résultats INDEX3 (BANKER/PLAYER/TIE)

        Returns:
            float: Entropie de Shannon en bits
        """
        if not fenetre_resultats:
            return 0.0

        # Filtrer les résultats vides
        resultats_valides = [r for r in fenetre_resultats if r and r.strip()]

        if not resultats_valides:
            return 0.0

        # Calculer distribution de probabilité
        counts = Counter(resultats_valides)
        total = len(resultats_valides)

        # Calculer entropie de Shannon: H = -Σ p(x) × log₂(p(x))
        entropie = 0.0
        for count in counts.values():
            if count > 0:
                p = count / total
                entropie -= p * math.log2(p)

        return entropie

    def _calculer_patterns_soe(self, index3_resultats: List[str]) -> List[str]:
        """
        Calcule les patterns S (Same), O (Opposite), E (Égalité) pour chaque main.
        Copié intégralement de analyseur_transitions_index5.py

        Args:
            index3_resultats: Liste des résultats INDEX3 (BANKER/PLAYER/TIE)

        Returns:
            list: Liste des patterns S/O/E où patterns[i] = pattern de la main i
        """
        if len(index3_resultats) < 2:
            return []

        patterns = [None]  # patterns[0] = None (pas de pattern pour main 0)
        dernier_non_tie = None

        for i in range(1, len(index3_resultats)):
            resultat_actuel = index3_resultats[i]
            resultat_precedent = index3_resultats[i-1]

            # Si le résultat actuel est TIE
            if resultat_actuel == 'TIE':
                patterns.append('E')  # patterns[i] = pattern de la main i
                continue

            # Si le résultat précédent est TIE, chercher le dernier non-TIE
            if resultat_precedent == 'TIE':
                # Chercher le dernier résultat non-TIE avant la position i-1
                dernier_non_tie = None
                for j in range(i-2, -1, -1):
                    if index3_resultats[j] != 'TIE':
                        dernier_non_tie = index3_resultats[j]
                        break

                # Si aucun résultat non-TIE trouvé, on ne peut pas déterminer le pattern
                if dernier_non_tie is None:
                    patterns.append('--')  # Indéterminé
                    continue

                # Comparer avec le dernier non-TIE
                if resultat_actuel == dernier_non_tie:
                    patterns.append('S')  # Same
                else:
                    patterns.append('O')  # Opposite
            else:
                # Comparaison normale (pas de TIE précédent)
                if resultat_actuel == resultat_precedent:
                    patterns.append('S')  # Same
                else:
                    patterns.append('O')  # Opposite

        return patterns
    
    # ========================================================================
    # PHASE 3: CALCULS RATIOS L4/L5
    # ========================================================================

    def _phase3_calculs_ratios(self) -> bool:
        """
        PHASE 3: Calcule les ratios L4/L5 pour chaque main.
        Copié et adapté de analyseur_transitions_index5.py

        Logique des ratios:
        - ratio_l4 = entropie_locale_l4 / entropie_globale
        - ratio_l5 = entropie_locale_l5 / entropie_globale

        Returns:
            bool: True si succès, False sinon
        """
        print("\n📊 PHASE 3: CALCULS RATIOS L4/L5")
        print("-" * 50)

        try:
            # Initialiser les structures de données pour les ratios
            self.ratios_l4 = {}  # {partie_id: {main_number: ratio_l4}}
            self.ratios_l5 = {}  # {partie_id: {main_number: ratio_l5}}
            self.entropies_globales = {}  # {partie_id: {main_number: entropie_globale}}

            nb_parties_traitees = 0
            nb_ratios_l4_calcules = 0
            nb_ratios_l5_calcules = 0

            print("🔄 Calcul des ratios L4/L5 pour toutes les parties...")

            for partie in self.donnees_brutes:
                partie_id = partie.get('partie_number', nb_parties_traitees)
                mains = partie.get('mains', [])

                if len(mains) < 5:  # Besoin d'au moins 5 mains
                    continue

                # Extraire les résultats INDEX3 pour calculer l'entropie globale
                index3_resultats = [main.get('index3_result', '') for main in mains]

                # Calculer entropies globales progressives pour chaque main
                entropies_globales_partie = self._calculer_entropies_globales_progressives(index3_resultats)
                self.entropies_globales[partie_id] = entropies_globales_partie

                # Calculer ratios L4 et L5 pour chaque main
                ratios_l4_partie = {}
                ratios_l5_partie = {}

                # Récupérer les entropies L4/L5 calculées en phase 2
                entropies_l4_partie = self.entropies_l4.get(partie_id, {})
                entropies_l5_partie = self.entropies_l5.get(partie_id, {})

                for main_number in entropies_globales_partie:
                    entropie_globale = entropies_globales_partie[main_number]

                    # Calculer ratio L4 si entropie L4 disponible
                    if main_number in entropies_l4_partie and entropie_globale > 1e-10:
                        entropie_l4 = entropies_l4_partie[main_number]
                        ratio_l4 = entropie_l4 / entropie_globale
                        ratios_l4_partie[main_number] = ratio_l4
                        nb_ratios_l4_calcules += 1

                    # Calculer ratio L5 si entropie L5 disponible
                    if main_number in entropies_l5_partie and entropie_globale > 1e-10:
                        entropie_l5 = entropies_l5_partie[main_number]
                        ratio_l5 = entropie_l5 / entropie_globale
                        ratios_l5_partie[main_number] = ratio_l5
                        nb_ratios_l5_calcules += 1

                self.ratios_l4[partie_id] = ratios_l4_partie
                self.ratios_l5[partie_id] = ratios_l5_partie

                nb_parties_traitees += 1

                # Affichage progression
                if nb_parties_traitees % 1000 == 0:
                    print(f"   📈 {nb_parties_traitees:,} parties traitées...")

            print(f"✅ Ratios calculés: {nb_parties_traitees:,} parties")
            print(f"   - Ratios L4: {nb_ratios_l4_calcules:,} valeurs")
            print(f"   - Ratios L5: {nb_ratios_l5_calcules:,} valeurs")
            print(f"   - Entropies globales: {sum(len(e) for e in self.entropies_globales.values()):,} valeurs")

            return True

        except Exception as e:
            print(f"❌ Erreur calculs ratios: {e}")
            import traceback
            traceback.print_exc()
            return False

    def _calculer_entropies_globales_progressives(self, index3_resultats: List[str]) -> Dict[int, float]:
        """
        Calcule l'entropie globale progressive pour chaque main.
        L'entropie globale à la main N = entropie de toutes les mains de 1 à N.

        Args:
            index3_resultats: Liste des résultats INDEX3 de la partie

        Returns:
            dict: {main_number: entropie_globale}
        """
        entropies_globales = {}

        # Calculer entropie globale progressive
        for i in range(1, len(index3_resultats)):  # Commencer à la main 1
            main_number = i

            # Séquence globale de la main 1 à la main i (incluse)
            sequence_globale = index3_resultats[1:i+1]  # Ignorer main 0

            # Calculer entropie de Shannon de cette séquence
            entropie_globale = self._calculer_entropie_locale(sequence_globale)

            entropies_globales[main_number] = entropie_globale

        return entropies_globales
    
    # ========================================================================
    # PHASE 4: CALCUL DIFF (À IMPLÉMENTER)
    # ========================================================================
    
    def _phase4_calcul_diff(self) -> bool:
        """
        PHASE 4: Calcule la variable DIFF = |L4-L5| et extrait les données.
        
        Returns:
            bool: True si succès, False sinon
        """
        print("\n📊 PHASE 4: CALCUL VARIABLE DIFF")
        print("-" * 50)
        
        # TODO: Implémenter le calcul DIFF
        # Adapter de analyse_complete_avec_diff.py
        
        print("⚠️  PHASE 4 À IMPLÉMENTER")
        print("   - Calcul DIFF = |L4-L5|")
        print("   - Extraction données S/O")
        print("   - Filtrage TIE")
        
        return True
    
    # ========================================================================
    # PHASES SUIVANTES (À IMPLÉMENTER)
    # ========================================================================
    
    def _phase5_analyse_conditions(self) -> bool:
        """PHASE 5: Analyse des conditions prédictives S/O."""
        print("\n📊 PHASE 5: ANALYSE CONDITIONS PRÉDICTIVES")
        print("-" * 50)
        print("⚠️  PHASE 5 À IMPLÉMENTER")
        return True
    
    def _phase6_correlations_essentielles(self) -> bool:
        """PHASE 6: Calcul des 6 corrélations essentielles uniquement."""
        print("\n📊 PHASE 6: CORRÉLATIONS ESSENTIELLES")
        print("-" * 50)
        print("⚠️  PHASE 6 À IMPLÉMENTER")
        return True
    
    def _phase7_generer_rapport(self) -> bool:
        """PHASE 7: Génération du rapport final."""
        print("\n📊 PHASE 7: GÉNÉRATION RAPPORT")
        print("-" * 50)
        print("⚠️  PHASE 7 À IMPLÉMENTER")
        return True

# ============================================================================
# III. FONCTION PRINCIPALE
# ============================================================================

def main():
    """
    Fonction principale - Lance l'analyse complète.
    """
    print("🚀 LANCEMENT ANALYSEUR BACCARAT AUTONOME")
    print("=" * 60)
    
    # Créer et lancer l'analyseur
    analyseur = AnalyseurBaccaratAutonome()
    
    # Lancer l'analyse complète
    succes = analyseur.analyser_complet()
    
    if succes:
        print("\n🎯 ANALYSE AUTONOME RÉUSSIE !")
        print("✅ Rapport généré avec succès")
    else:
        print("\n❌ ANALYSE AUTONOME ÉCHOUÉE")
        print("⚠️  Vérifiez les erreurs ci-dessus")
    
    print("\n" + "=" * 60)

# ============================================================================
# IV. POINT D'ENTRÉE
# ============================================================================

if __name__ == "__main__":
    main()
